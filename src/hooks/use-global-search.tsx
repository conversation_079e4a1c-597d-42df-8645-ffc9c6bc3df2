import { useState, useCallback } from 'react';
import { useDebounceSearch } from '@/hooks/use-debounced-search';
import React from 'react';

export interface GlobalSearchResult {
  database: string;
  count: number;
}

export function useGlobalSearch() {
  const [results, setResults] = useState<GlobalSearchResult[]>([]);

  const cacheRef = React.useRef<Record<string, GlobalSearchResult[]>>({});
  const abortRef = React.useRef<AbortController | null>(null);

  // Memoize the onSearch function to prevent infinite re-renders
  const onSearch = useCallback(async (kw: string) => {
    if (!kw) {
      setResults([]);
      return;
    }
    // If we already have cache for same kw, use it
    if (cacheRef.current[kw]) {
      setResults(cacheRef.current[kw]);
      return;
    }
    try {
      abortRef.current?.abort();
      const controller = new AbortController();
      abortRef.current = controller;
      const res = await fetch(`/api/global-search?q=${encodeURIComponent(kw)}`, { signal: controller.signal }).then((r) => r.json());
      if (res.success) {
        cacheRef.current[kw] = res.data as GlobalSearchResult[];
        setResults(res.data as GlobalSearchResult[]);
      }
    } catch (e) {
      if ((e as any)?.name !== 'AbortError') {
        console.error('global search failed', e);
      }
    }
  }, []); // Empty dependency array since setResults is stable and refs don't change

  const { query, setQuery, isSearching } = useDebounceSearch({
    delay: 300,
    minLength: 2,
    onSearch,
  });

  return { query, setQuery, results, isSearching };
} 
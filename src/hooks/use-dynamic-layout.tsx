import { useState, useEffect, useLayoutEffect, useRef, useCallback } from 'react';

interface LayoutState {
  headerTop: number;
  tableMarginTop: number;
  tableMaxHeight: string;
  isLayoutStable: boolean;
}

interface UseDynamicLayoutProps {
  showStats: boolean;
  filterOpen: boolean;
  tableHeadersLength: number;
}

export function useDynamicLayout({ showStats, filterOpen, tableHeadersLength }: UseDynamicLayoutProps) {
  const [layoutState, setLayoutState] = useState<LayoutState>({
    headerTop: 140,
    tableMarginTop: 0,
    tableMaxHeight: 'calc(100vh - 240px)',
    isLayoutStable: false
  });

  const databaseTitleRef = useRef<HTMLDivElement>(null);
  const headerFixedRef = useRef<HTMLDivElement>(null);
  const statsPanelRef = useRef<HTMLDivElement>(null);
  const resizeObserverRef = useRef<ResizeObserver | null>(null);

  // 计算布局的核心函数
  const calculateLayout = useCallback(() => {
    if (!databaseTitleRef.current) return;

    const titleElement = databaseTitleRef.current;
    const titleBottom = titleElement.offsetTop + titleElement.offsetHeight;
    
    // 计算表头位置 - 始终基于数据库标题区域的底部
    const newHeaderTop = titleBottom;
    
    // 计算数据表格位置 - 始终基于表头的底部
    let newTableMarginTop = newHeaderTop;
    let newTableMaxHeight = 'calc(100vh - 240px)';
    
    // 如果表头元素存在，使用表头的实际底部位置
    if (headerFixedRef.current) {
      const headerElement = headerFixedRef.current;
      const headerBottom = headerElement.offsetTop + headerElement.offsetHeight;
      newTableMarginTop = headerBottom;
    }
    
    // 动态计算表格最大高度，考虑StatsPanel的影响
    if (showStats && statsPanelRef.current) {
      const statsHeight = statsPanelRef.current.offsetHeight;
      const baseHeight = 240; // 基础高度（导航栏、分页等）
      const dynamicHeight = baseHeight + statsHeight + 20; // 添加一些缓冲
      newTableMaxHeight = `calc(100vh - ${dynamicHeight}px)`;
    } else {
      // StatsPanel隐藏时的标准高度
      newTableMaxHeight = 'calc(100vh - 240px)';
    }
    
    // 添加调试日志
    console.log('Layout calculation:', {
      showStats,
      titleBottom,
      newHeaderTop,
      newTableMarginTop,
      newTableMaxHeight,
      hasHeaderRef: !!headerFixedRef.current,
      hasStatsRef: !!statsPanelRef.current
    });
    
    setLayoutState(prev => ({
      headerTop: newHeaderTop,
      tableMarginTop: newTableMarginTop,
      tableMaxHeight: newTableMaxHeight,
      isLayoutStable: true
    }));
  }, [showStats]);

  // 使用useLayoutEffect确保在DOM更新后立即计算
  useLayoutEffect(() => {
    calculateLayout();
  }, [calculateLayout, showStats, filterOpen, tableHeadersLength]);

  // 监听数据库标题区域的大小变化
  useEffect(() => {
    if (!databaseTitleRef.current) return;

    const titleElement = databaseTitleRef.current;
    
    // 创建ResizeObserver监听大小变化
    resizeObserverRef.current = new ResizeObserver(() => {
      // 使用requestAnimationFrame确保在下一帧执行，避免布局抖动
      requestAnimationFrame(calculateLayout);
    });
    
    resizeObserverRef.current.observe(titleElement);
    
    // 监听窗口大小变化
    const handleResize = () => {
      requestAnimationFrame(calculateLayout);
    };
    
    window.addEventListener('resize', handleResize);
    
    return () => {
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
      }
      window.removeEventListener('resize', handleResize);
    };
  }, [calculateLayout]);

  // 当showStats状态变化时，延迟重新计算以确保DOM完全更新
  useEffect(() => {
    const timer = setTimeout(() => {
      calculateLayout();
    }, 100); // 100ms延迟确保DOM完全更新
    
    return () => clearTimeout(timer);
  }, [showStats, calculateLayout]);

  // 监听表头元素的变化，确保表头位置变化时重新计算
  useEffect(() => {
    if (!headerFixedRef.current) return;

    const headerElement = headerFixedRef.current;
    
    const resizeObserver = new ResizeObserver(() => {
      requestAnimationFrame(calculateLayout);
    });
    
    resizeObserver.observe(headerElement);
    
    return () => {
      resizeObserver.disconnect();
    };
  }, [calculateLayout]);

  return {
    layoutState,
    refs: {
      databaseTitleRef,
      headerFixedRef,
      statsPanelRef
    },
    recalculateLayout: calculateLayout
  };
} 
"use client";

import { useState, useEffect } from 'react';
import { fullPermissionDebug } from '@/lib/permission-debug';
import { useAuth } from '@/lib/auth';

export default function DebugUSClassPage() {
  const { user } = useAuth();
  const [debugResult, setDebugResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const runDebug = async () => {
    setLoading(true);
    try {
      console.log('🚀 Starting us_class access debugging...');
      const result = await fullPermissionDebug('us_class', user?.membershipType);
      setDebugResult(result);
    } catch (error) {
      console.error('Error occurred during debugging:', error);
      setDebugResult({ error: error instanceof Error ? error.message : String(error) });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // 页面加载时自动运行调试
    runDebug();
  }, [user]);

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8 text-center">US Class Access Debug Tool</h1>
        
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Current User Status</h2>
          <div className="bg-gray-100 p-4 rounded">
            <pre>{JSON.stringify(user, null, 2)}</pre>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Debug Operations</h2>
          <button
            onClick={runDebug}
            disabled={loading}
            className="bg-blue-500 text-white px-6 py-3 rounded hover:bg-blue-600 disabled:opacity-50"
          >
            {loading ? 'Debugging...' : 'Run Debug Again'}
          </button>
        </div>

        {debugResult && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Debug Results</h2>
            <div className="space-y-4">
              
              <div className="border-l-4 border-blue-500 pl-4">
                <h3 className="font-semibold">1. Permission Check</h3>
                <p className={debugResult.hasPermission ? 'text-green-600' : 'text-red-600'}>
                  {debugResult.hasPermission ? '✅ Has access permission' : '❌ No access permission'}
                </p>
              </div>

              <div className="border-l-4 border-green-500 pl-4">
                <h3 className="font-semibold">2. Configuration Retrieval</h3>
                <p className={debugResult.hasConfig ? 'text-green-600' : 'text-red-600'}>
                  {debugResult.hasConfig ? '✅ Configuration retrieved successfully' : '❌ Configuration retrieval failed'}
                </p>
                {debugResult.config && (
                  <div className="mt-2 bg-gray-100 p-2 rounded">
                    <pre className="text-sm">{JSON.stringify(debugResult.config, null, 2)}</pre>
                  </div>
                )}
              </div>

              <div className="border-l-4 border-purple-500 pl-4">
                <h3 className="font-semibold">3. Data API Test</h3>
                <p className={debugResult.hasData ? 'text-green-600' : 'text-red-600'}>
                  {debugResult.hasData ? '✅ Data API call successful' : '❌ Data API call failed'}
                </p>
                {debugResult.dataCount && (
                  <p className="text-gray-600">Total data count: {debugResult.dataCount}</p>
                )}
                {debugResult.error && (
                  <div className="mt-2 bg-red-100 p-2 rounded">
                    <p className="text-red-700 text-sm">{debugResult.error}</p>
                  </div>
                )}
              </div>

              <div className="border-l-4 border-gray-500 pl-4">
                <h3 className="font-semibold">Full Results</h3>
                <div className="bg-gray-100 p-4 rounded">
                  <pre className="text-xs overflow-auto">{JSON.stringify(debugResult, null, 2)}</pre>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="bg-yellow-100 border border-yellow-400 rounded-lg p-4 mt-6">
          <h3 className="font-semibold text-yellow-800 mb-2">Debug Instructions</h3>
          <p className="text-yellow-700 text-sm">
            This page is used to diagnose us_class database access issues. Please open the browser developer tools console to view detailed debugging information.
          </p>
        </div>

        <div className="mt-6 text-center">
          <a 
            href="/data/list/us_class" 
            className="bg-green-500 text-white px-6 py-3 rounded hover:bg-green-600 inline-block"
          >
            Access US Class Page
          </a>
        </div>
      </div>
    </div>
  );
} 
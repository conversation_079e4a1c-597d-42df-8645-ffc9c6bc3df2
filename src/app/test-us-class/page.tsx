"use client";

import { useState, useEffect } from 'react';

export default function TestUSClassPage() {
  const [apiResult, setApiResult] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const testAPI = async () => {
      try {
        console.log('🧪 Starting us_class API access test...');
        
        const response = await fetch('/api/data/us_class?page=1&limit=5');
        const result = await response.json();
        
        console.log('🧪 API Response:', result);
        setApiResult(result);
      } catch (error) {
        console.error('🧪 API test failed:', error);
        setApiResult({ error: error instanceof Error ? error.message : String(error) });
      } finally {
        setLoading(false);
      }
    };

    testAPI();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">US Class API Test</h1>
        
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Test Results</h2>
          
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p>Testing...</p>
            </div>
          ) : (
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold">API Status</h3>
                <p className={apiResult?.success ? 'text-green-600' : 'text-red-600'}>
                  {apiResult?.success ? '✅ Success' : '❌ Failed'}
                </p>
              </div>
              
              {apiResult?.data && (
                <div>
                  <h3 className="font-semibold">Data Count</h3>
                  <p className="text-blue-600">{apiResult.data.length} records</p>
                </div>
              )}
              
              {apiResult?.pagination && (
                <div>
                  <h3 className="font-semibold">Total Data</h3>
                  <p className="text-green-600">{apiResult.pagination.totalCount} total records</p>
                </div>
              )}
              
              {apiResult?.error && (
                <div>
                  <h3 className="font-semibold text-red-600">Error Message</h3>
                  <p className="text-red-600">{apiResult.error}</p>
                </div>
              )}
              
              <div>
                <h3 className="font-semibold">Full Response</h3>
                <pre className="bg-gray-100 p-4 rounded text-xs overflow-auto">
                  {JSON.stringify(apiResult, null, 2)}
                </pre>
              </div>
            </div>
          )}
        </div>
        
        <div className="mt-6 text-center">
          <p className="text-gray-600 text-sm">
            This page verifies whether the us_class API is working properly, without complex permission checks
          </p>
        </div>
      </div>
    </div>
  );
} 
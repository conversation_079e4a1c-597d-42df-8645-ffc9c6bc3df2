'use client';

import { useState, useEffect } from 'react';
import { getDatabaseConfigs } from '@/lib/permissions';

export default function TestConfigPage() {
  const [configs, setConfigs] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchConfigs = async () => {
      try {
        console.log('🔍 开始获取数据库配置...');

        // 直接调用 API 而不是通过 getDatabaseConfigs
        const response = await fetch('/api/config/databases');
        console.log('📡 API 响应状态:', response.status);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log('📋 API 响应数据:', result);

        if (!result.success) {
          throw new Error(result.error || 'API 返回失败');
        }

        console.log('✅ 获取配置成功:', result.data);
        setConfigs(result.data);
      } catch (err) {
        console.error('❌ 获取配置失败:', err);
        setError(err instanceof Error ? err.message : '未知错误');
      } finally {
        setLoading(false);
      }
    };

    fetchConfigs();
  }, []);

  if (loading) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">配置测试页面</h1>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p>加载中...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">配置测试页面</h1>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <strong>错误:</strong> {error}
        </div>
      </div>
    );
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">配置测试页面</h1>
      
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">数据库配置</h2>
        <p className="text-gray-600">找到 {Object.keys(configs).length} 个数据库配置</p>
      </div>

      <div className="space-y-4">
        {Object.entries(configs).map(([code, config]) => (
          <div key={code} className="border rounded-lg p-4 bg-gray-50">
            <h3 className="font-semibold text-lg">{config.name}</h3>
            <div className="mt-2 space-y-1 text-sm">
              <p><strong>代码:</strong> {code}</p>
              <p><strong>分类:</strong> {config.category}</p>
              <p><strong>描述:</strong> {config.description}</p>
              <p><strong>访问级别:</strong> {config.accessLevel}</p>
              <p><strong>图标:</strong> {config.icon}</p>
              {config.defaultSort && (
                <p><strong>默认排序:</strong> {JSON.stringify(config.defaultSort)}</p>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

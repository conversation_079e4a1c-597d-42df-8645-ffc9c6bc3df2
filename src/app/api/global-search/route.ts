import { type NextRequest, NextResponse } from 'next/server';
import { getDatabaseConfigs } from '@/lib/permissions';
import { getDatabaseConfig } from '@/lib/configCache';
import { getDynamicModel, isPrismaModel } from '@/lib/staticTableMappingService';
import { esClient } from '@/lib/elasticsearch';

/**
 * GET /api/global-search?q=<keyword>
 * 返回每个数据库匹配到的记录数，用于首页/导航综合搜索。
 */
export async function GET(req: NextRequest) {
  try {
    const q = req.nextUrl.searchParams.get('q')?.trim();
    if (!q) {
      return NextResponse.json({ success: false, error: 'q required' }, { status: 400 });
    }

    // 获取数据库配置 - 统一使用小写代码
    const databaseConfigs = await getDatabaseConfigs();
    const dbCodes = Object.keys(databaseConfigs).map(code => code.toLowerCase());

    // 尝试使用 Elasticsearch，如果失败则回退到 Prisma
    try {
      // 1. 获取每库可搜索字段
      const configs = await Promise.all(dbCodes.map((c) => getDatabaseConfig(c)));

      // 2. 构建 msearch 请求
      const searches: any[] = [];
      configs.forEach((cfg, idx) => {
        const code = dbCodes[idx];
        const fields = cfg.fields
          .filter((f) => f.isSearchable && f.searchType === 'contains')
          .map((f) => f.fieldName);

        // 添加索引头 - 使用小写索引名
        searches.push({ index: `db-${code}` });

        // 添加查询体
        if (fields.length === 0) {
          searches.push({ size: 0, query: { match_none: {} } });
        } else {
          const should = fields.map((f) => ({
            wildcard: {
              [`${f}.substr`]: {
                value: `*${q}*`,
                case_insensitive: true,
              },
            },
          }));
          searches.push({ size: 0, query: { bool: { should } } });
        }
      });

      // 3. 执行 msearch - 使用正确的ES 8.x API
      const response = await esClient.msearch({
        body: searches
      });

      // ES 8.x 的响应结构处理
      const responses = (response as any).body?.responses || (response as any).responses || [];
      const data = responses.map((resp: any, idx: number) => {
        if (resp.error) {
          console.warn(`ES 搜索错误 ${dbCodes[idx]}:`, resp.error);
          return { database: dbCodes[idx], count: 0 };
        }
        const total = resp.hits?.total?.value ?? 0;
        return { database: dbCodes[idx], count: total };
      });

      return NextResponse.json({ success: true, data });
      
    } catch (esError) {
      console.warn('Elasticsearch 搜索失败，回退到 Prisma:');
      console.warn('错误详情:', esError);

      // 回退到 Prisma 搜索 - 重构版本：使用动态模型，不依赖database字段
      const data = await Promise.all(dbCodes.map(async (dbCode) => {
        try {
          const config = await getDatabaseConfig(dbCode);
          const searchableFields = config.fields.filter(f => f.isSearchable && f.searchType === 'contains');

          if (searchableFields.length === 0) {
            return { database: dbCode, count: 0 };
          }

          // 使用动态模型获取 - 重构版本
          const model = await getDynamicModel(dbCode);
          if (!isPrismaModel(model)) {
            console.warn(`数据库模型未找到: ${dbCode}`);
            return { database: dbCode, count: 0 };
          }

          // 构建 OR 查询条件 - 不再需要database字段过滤
          const orConditions = searchableFields.map(field => ({
            [field.fieldName]: {
              contains: q,
              mode: 'insensitive' as const
            }
          }));

          const count = await (model as any).count({
            where: {
              OR: orConditions,
              isActive: true, // 只搜索活跃记录
            }
          });

          return { database: dbCode, count };
        } catch (dbError) {
          console.error(`搜索数据库 ${dbCode} 时出错:`, dbError);
          return { database: dbCode, count: 0 };
        }
      }));

      return NextResponse.json({ success: true, data });
    }
  } catch (err) {
    console.error('全局搜索错误', err);
    return NextResponse.json({ success: false, error: '内部错误' }, { status: 500 });
  }
}
import { type NextRequest, NextResponse } from 'next/server';
import { getDynamicModel, isPrismaModel, getAllDatabaseCodes } from '@/lib/dynamicTableMapping';
import { getDatabaseConfig } from '@/lib/configCache';
import { esClient } from '@/lib/elasticsearch';

/**
 * GET /api/global-search?q=<keyword>
 * 返回每个数据库匹配到的记录数，用于首页/导航综合搜索。
 * 重构版本：使用动态表映射系统，移除对database字段的依赖
 */
export async function GET(req: NextRequest) {
  try {
    const q = req.nextUrl.searchParams.get('q')?.trim();
    if (!q) {
      return NextResponse.json({ success: false, error: 'q required' }, { status: 400 });
    }

    // 获取所有可用的数据库代码
    const dbCodes = getAllDatabaseCodes();

    // 尝试使用 Elasticsearch，如果失败则回退到 Prisma
    try {
      // 1. 获取每库可搜索字段
      const configs = await Promise.all(dbCodes.map((c) => getDatabaseConfig(c)));

             // 2. 构建 msearch 请求
       const searches: any[] = [];
       configs.forEach((cfg, idx) => {
         const code = dbCodes[idx];
         const fields = cfg.fields
           .filter((f) => f.isSearchable && f.searchType === 'contains')
           .map((f) => f.fieldName);

         // 添加索引头
         searches.push({ index: `db-${code.toLowerCase()}` });

         // 添加查询体
         if (fields.length === 0) {
           searches.push({ size: 0, query: { match_none: {} } });
         } else {
           const should = fields.map((f) => ({
             wildcard: {
               [`${f}.substr`]: {
                 value: `*${q}*`,
                 case_insensitive: true,
               },
             },
           }));
           searches.push({ size: 0, query: { bool: { should } } });
         }
       });

       // 3. 执行 msearch - 使用正确的ES 8.x API
       const response = await esClient.msearch({
         body: searches
       });

       // ES 8.x 的响应结构可能不同，尝试多种访问方式
       const responses = (response as any).body?.responses || (response as any).responses || [];
       const data = responses.map((resp: any, idx: number) => {
         if (resp.error) {
           console.warn(`ES search error for ${dbCodes[idx]}:`, resp.error);
           return { database: dbCodes[idx], count: 0 };
         }
         const total = resp.hits?.total?.value ?? 0;
         return { database: dbCodes[idx], count: total };
       });

      return NextResponse.json({ success: true, data });
    } catch (esError) {
      console.warn('Elasticsearch search failed, falling back to Prisma:');
      console.warn('Error details:', esError);

      // 回退到 Prisma 搜索 - 重构版本：使用动态模型，不依赖database字段
      const data = await Promise.all(dbCodes.map(async (dbCode) => {
        try {
          const config = await getDatabaseConfig(dbCode);
          const searchableFields = config.fields.filter(f => f.isSearchable && f.searchType === 'contains');

          if (searchableFields.length === 0) {
            return { database: dbCode, count: 0 };
          }

          // 使用动态模型获取 - 重构版本
          const model = getDynamicModel(dbCode);
          if (!isPrismaModel(model)) {
            console.warn(`Model not found for database: ${dbCode}`);
            return { database: dbCode, count: 0 };
          }

          // 构建 OR 查询条件 - 不再需要database字段过滤
          const orConditions = searchableFields.map(field => ({
            [field.fieldName]: {
              contains: q,
              mode: 'insensitive' as const
            }
          }));

          const count = await (model as any).count({
            where: {
              OR: orConditions,
              isActive: true, // 只搜索活跃记录
            }
          });

          return { database: dbCode, count };
        } catch (dbError) {
          console.error(`Error searching database ${dbCode}:`, dbError);
          return { database: dbCode, count: 0 };
        }
      }));

      return NextResponse.json({ success: true, data });
    }
  } catch (err) {
    console.error('global-search error', err);
    return NextResponse.json({ success: false, error: 'internal error' }, { status: 500 });
  }
} 
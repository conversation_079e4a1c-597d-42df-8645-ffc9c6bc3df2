import { NextResponse } from 'next/server';
import { db } from '@/lib/prisma';

// 数据库配置缓存
let databaseConfigsCache: Record<string, any> | null = null;
let configsCacheExpiry: number = 0;
const CACHE_TTL = 10 * 60 * 1000; // 10分钟缓存

// 获取数据库图标
function getDatabaseIcon(code: string): string {
  const icons: Record<string, string> = {
    deviceCNImported: '🏥',
    us_pmn: '🇺🇸',
  };
  return icons[code] || '📊';
}

export async function GET() {
  try {
    const now = Date.now();

    // 如果缓存有效，直接返回
    if (databaseConfigsCache && now < configsCacheExpiry) {
      return NextResponse.json({
        success: true,
        data: databaseConfigsCache
      });
    }

    // 从数据库获取配置
    const configs = await db.databaseConfig.findMany({
      where: { isActive: true },
      select: {
        code: true,
        name: true,
        category: true,
        description: true,
        accessLevel: true,
        defaultSort: true
      },
    });

    // 构建配置对象
    const configsMap: Record<string, any> = {};
    configs.forEach(config => {
      configsMap[config.code] = {
        name: config.name,
        category: config.category,
        description: config.description || '',
        accessLevel: config.accessLevel,
        defaultSort: config.defaultSort || null,
        icon: getDatabaseIcon(config.code),
      };
    });

    // 设置缓存
    databaseConfigsCache = configsMap;
    configsCacheExpiry = now + CACHE_TTL;

    return NextResponse.json({
      success: true,
      data: configsMap
    });

  } catch (error) {
    console.error('获取数据库配置失败:', error);
    
    // 回退到硬编码配置
    const fallbackConfigs = {
      deviceCNImported: {
        name: '医疗器械模板',
        category: 'Regulation',
        description: '新建医疗器械数据库时的基础模板',
        accessLevel: 'free',
        icon: '🏥',
      },
      us_pmn: {
        name: '美国PMN(510k)',
        category: '全球器械',
        description: '美国FDA PMN(510k)医疗器械审批信息',
        accessLevel: 'premium',
        icon: '🇺🇸',
      },
    };

    return NextResponse.json({
      success: true,
      data: fallbackConfigs
    });
  }
}

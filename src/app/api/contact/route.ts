import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';

// Rate limiting: Store recent submissions in memory (in production, use Redis)
const recentSubmissions = new Map<string, number[]>();
const RATE_LIMIT_WINDOW = 60 * 60 * 1000; // 1 hour
const MAX_SUBMISSIONS_PER_HOUR = 5;

// Simple email validation
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Rate limiting check
function checkRateLimit(ip: string): boolean {
  const now = Date.now();
  const submissions = recentSubmissions.get(ip) || [];
  
  // Remove old submissions outside the window
  const recentSubmissionsInWindow = submissions.filter(
    timestamp => now - timestamp < RATE_LIMIT_WINDOW
  );
  
  if (recentSubmissionsInWindow.length >= MAX_SUBMISSIONS_PER_HOUR) {
    return false;
  }
  
  // Add current submission
  recentSubmissionsInWindow.push(now);
  recentSubmissions.set(ip, recentSubmissionsInWindow);
  
  return true;
}

// Content filtering for spam/malicious content
function containsSuspiciousContent(text: string): boolean {
  const suspiciousPatterns = [
    /https?:\/\/[^\s]+/gi, // URLs
    /\b(?:viagra|cialis|casino|lottery|winner|congratulations)\b/gi, // Common spam words
    /<script|javascript:|onclick=/gi, // Script injection attempts
    /\b(?:admin|root|password|login)\b/gi, // System-related terms
  ];
  
  return suspiciousPatterns.some(pattern => pattern.test(text));
}

export async function POST(request: NextRequest) {
  try {
    // Get client IP for rate limiting
    const ip = request.headers.get('x-forwarded-for') || 
               request.headers.get('x-real-ip') || 
               'unknown';

    // Check rate limit
    if (!checkRateLimit(ip)) {
      return NextResponse.json(
        { error: 'Too many submissions. Please try again later.' },
        { status: 429 }
      );
    }

    const body = await request.json();
    const { name, email, subject, category, message, captcha } = body;

    // Validation
    if (!name || !email || !subject || !category || !message) {
      return NextResponse.json(
        { error: 'All fields are required.' },
        { status: 400 }
      );
    }

    if (!isValidEmail(email)) {
      return NextResponse.json(
        { error: 'Invalid email address.' },
        { status: 400 }
      );
    }

    // Content length validation
    if (name.length > 100 || subject.length > 200 || message.length > 2000) {
      return NextResponse.json(
        { error: 'Content too long. Please shorten your message.' },
        { status: 400 }
      );
    }

    // Content filtering
    const allContent = `${name} ${email} ${subject} ${message}`;
    if (containsSuspiciousContent(allContent)) {
      return NextResponse.json(
        { error: 'Message contains inappropriate content.' },
        { status: 400 }
      );
    }

    // Sanitize input
    const sanitizedData = {
      name: name.trim().substring(0, 100),
      email: email.trim().toLowerCase().substring(0, 255),
      subject: subject.trim().substring(0, 200),
      category: category.trim(),
      message: message.trim().substring(0, 2000),
      ip: ip.substring(0, 45), // IPv6 max length
      userAgent: request.headers.get('user-agent')?.substring(0, 500) || 'unknown',
      timestamp: new Date(),
    };

    // Save to database
    try {
      await db.contactSubmission.create({
        data: sanitizedData,
      });
    } catch (dbError) {
      console.error('Database error:', dbError);
      // Don't expose database errors to client
      return NextResponse.json(
        { error: 'Failed to save message. Please try again.' },
        { status: 500 }
      );
    }

    // In a real application, you would also:
    // 1. Send email notification to admin
    // 2. Send confirmation email to user
    // 3. Log the submission for analytics

    // Simulate email sending (replace with actual email service)
    try {
      await sendNotificationEmail(sanitizedData);
    } catch (emailError) {
      console.error('Email notification failed:', emailError);
      // Don't fail the request if email fails
    }

    return NextResponse.json(
      { 
        success: true, 
        message: 'Your message has been sent successfully. We will get back to you within 24 hours.' 
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Contact form error:', error);
    return NextResponse.json(
      { error: 'Internal server error. Please try again later.' },
      { status: 500 }
    );
  }
}

// Mock email sending function (replace with actual email service like SendGrid, AWS SES, etc.)
async function sendNotificationEmail(data: any) {
  // In production, implement actual email sending
  console.log('New contact form submission:', {
    name: data.name,
    email: data.email,
    subject: data.subject,
    category: data.category,
    timestamp: data.timestamp,
  });
  
  // Example with a hypothetical email service:
  /*
  await emailService.send({
    to: '<EMAIL>',
    subject: `New Contact Form: ${data.subject}`,
    template: 'contact-notification',
    data: {
      name: data.name,
      email: data.email,
      subject: data.subject,
      category: data.category,
      message: data.message,
      timestamp: data.timestamp,
    }
  });
  */
}

// Handle other HTTP methods
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

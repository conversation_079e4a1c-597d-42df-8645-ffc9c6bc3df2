import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { getSession } from '@/lib/session';
import { AnalyticsQueries, analyticsCache } from '@/lib/analytics-cache';

export async function GET(request: NextRequest) {
  try {
    // 检查权限 - 只有管理员或高级用户可以查看分析数据
    const session = await getSession();
    if (!session?.userId) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get('range') || '7d'; // 7d, 30d, 90d
    const database = searchParams.get('database');

    // 计算时间范围
    const now = new Date();
    let startDate: Date;
    
    switch (timeRange) {
      case '1d':
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }

    // 构建查询条件
    const whereCondition: any = {
      createdAt: {
        gte: startDate,
        lte: now,
      },
    };

    if (database) {
      whereCondition.database = database;
    }

    // 使用缓存优化的并行查询
    const [
      basicStats,
      topDatabases,
      bounceRate,
      topPages,
      eventTypes,
      hourlyStats,
    ] = await Promise.all([
      // 基础统计数据（缓存优化）
      AnalyticsQueries.getCachedBasicStats(timeRange, database),

      // 热门数据库（缓存优化）
      AnalyticsQueries.getCachedTopDatabases(timeRange, 10),

      // 跳出率（缓存优化）
      AnalyticsQueries.getCachedBounceRate(timeRange, database),

      // 热门页面
      db.activityLog.groupBy({
        by: ['path'],
        where: whereCondition,
        _count: { path: true },
        orderBy: { _count: { path: 'desc' } },
        take: 10,
      }),

      // 事件类型统计
      db.activityLog.groupBy({
        by: ['eventType'],
        where: {
          ...whereCondition,
          eventType: { not: null },
        },
        _count: { eventType: true },
        orderBy: { _count: { eventType: 'desc' } },
      }),

      // 按小时统计 (最近24小时)
      db.$queryRaw`
        SELECT
          DATE_TRUNC('hour', "createdAt") as hour,
          COUNT(*) as count
        FROM "ActivityLog"
        WHERE "createdAt" >= ${new Date(now.getTime() - 24 * 60 * 60 * 1000)}
          AND "createdAt" <= ${now}
          ${database ? db.$queryRaw`AND "database" = ${database}` : db.$queryRaw``}
        GROUP BY DATE_TRUNC('hour', "createdAt")
        ORDER BY hour DESC
        LIMIT 24
      ` as Array<{ hour: Date; count: bigint }>,
    ]);

    // 格式化响应数据
    const stats = {
      overview: {
        totalVisits: basicStats.totalVisits,
        uniqueVisitors: basicStats.uniqueVisitors,
        pageViews: basicStats.pageViews,
        searchEvents: basicStats.searchEvents,
        bounceRate: Math.round(bounceRate * 100) / 100,
        avgSessionDuration: 0, // 可以后续计算
      },
      topDatabases,
      topPages: topPages.map((item: any) => ({
        path: item.path,
        visits: item._count.path,
      })),
      eventTypes: eventTypes.map((item: any) => ({
        type: item.eventType || 'Unknown',
        count: item._count.eventType,
      })),
      hourlyStats: hourlyStats.map((item: any) => ({
        hour: item.hour,
        count: Number(item.count),
      })).reverse(),
      timeRange,
      generatedAt: now.toISOString(),
    };

    return NextResponse.json({
      success: true,
      data: stats,
    });

  } catch (error) {
    console.error('Analytics stats error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch analytics data' },
      { status: 500 }
    );
  }
}

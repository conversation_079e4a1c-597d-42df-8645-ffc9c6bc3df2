import { NextResponse } from 'next/server';
import { deleteSession } from '@/lib/session';
import { db } from '@/lib/prisma';

export async function POST(request: Request) {
  try {
    await deleteSession();
    // 日志采集
    const ip = request.headers.get('x-forwarded-for')?.split(',')[0].trim() || request.headers.get('x-real-ip')?.trim() || '127.0.0.1';
    const userAgent = request.headers.get('user-agent') || undefined;
    const sessionId = request.headers.get('cookie')?.split(';').find(c => c.trim().startsWith('sessionId='))?.split('=')[1] || undefined;
    await db.activityLog.create({
      data: {
        ip,
        userAgent,
        path: '/api/auth/logout',
        method: 'POST',
        eventType: 'logout',
        sessionId,
      }
    });
    return NextResponse.json({ success: true, message: 'Logged out successfully' });
  } catch (error) {
    console.error('Logout Error:', error);
    return NextResponse.json({ success: false, error: 'Internal server error' }, { status: 500 });
  }
} 
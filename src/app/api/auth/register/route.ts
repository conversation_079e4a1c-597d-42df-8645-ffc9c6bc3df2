import { NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import bcrypt from 'bcrypt';
import { createSession } from '@/lib/session';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { email, password, name } = body;

    if (!email || !password || !name) {
      return NextResponse.json({ success: false, error: 'Missing required fields' }, { status: 400 });
    }

    if (password.length < 6) {
      return NextResponse.json({ success: false, error: 'Password must be at least 6 characters long' }, { status: 400 });
    }

    const existingUser = await db.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      return NextResponse.json({ success: false, error: 'User already exists' }, { status: 409 });
    }

    const hashedPassword = await bcrypt.hash(password, 10);

    const user = await db.user.create({
      data: {
        email,
        password: hashedPassword,
        name,
      },
    });

    // 创建会话
    const { password: _, ...userWithoutPassword } = user;
    await createSession(String(user.id), userWithoutPassword);

    // 日志采集
    const ip = request.headers.get('x-forwarded-for')?.split(',')[0].trim() || request.headers.get('x-real-ip')?.trim() || '127.0.0.1';
    const userAgent = request.headers.get('user-agent') || undefined;
    const sessionId = request.headers.get('cookie')?.split(';').find(c => c.trim().startsWith('sessionId='))?.split('=')[1] || undefined;
    await db.activityLog.create({
      data: {
        ip,
        userAgent,
        path: '/api/auth/register',
        method: 'POST',
        eventType: 'register',
        sessionId,
      }
    });

    return NextResponse.json({ success: true, user: userWithoutPassword });

  } catch (error) {
    console.error('Registration Error:', error);
    return NextResponse.json({ success: false, error: 'Internal server error' }, { status: 500 });
  }
} 
import { type NextRequest, NextResponse } from 'next/server';
import { getDynamicModel, isPrismaModel, validateDatabaseCode, getDatabaseAccessLevel } from '@/lib/dynamicTableMapping';
import { checkPermissions } from '@/lib/server/permissions';
import { buildMedicalDeviceWhere } from '@/lib/server/buildMedicalDeviceWhere';
import { getDatabaseConfig } from '@/lib/configCache';

export const dynamic = 'force-dynamic';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const { database } = await params;

    // 使用新的统一验证函数
    const validationError = validateDatabaseCode(database);
    if (validationError) {
      return NextResponse.json(
        { success: false, error: validationError.error },
        { status: validationError.status }
      );
    }

    // 权限检查
    const requiredLevel = getDatabaseAccessLevel(database) as "free" | "premium" | "enterprise";
    const hasAccess = await checkPermissions(requiredLevel);
    if (!hasAccess) {
      return NextResponse.json(
        { success: false, error: 'Permission denied' },
        { status: 403 }
      );
    }

    // 使用动态模型获取
    const model = getDynamicModel(database);
    if (!isPrismaModel(model)) {
      return NextResponse.json(
        { success: false, error: 'Model not found or invalid' },
        { status: 500 }
      );
    }

    // 获取配置
    const config = await getDatabaseConfig(database);
    const { searchParams } = new URL(request.url);
    
    // 统一where条件 - 重构版本：不再需要database字段过滤
    const where = buildMedicalDeviceWhere(searchParams, config);

    // 基础统计
    const totalCount = await (model as any).count({ where });
    const activeCount = await (model as any).count({ 
      where: { ...where, isActive: true } 
    });

    // 按类别统计（如果存在category字段）
    let categoryStats = [];
    try {
      categoryStats = await (model as any).groupBy({
        by: ['category'],
        where: { ...where, isActive: true, category: { not: null } },
        _count: { category: true },
        orderBy: { _count: { category: 'desc' } },
        take: 10,
      });
    } catch (error) {
      console.log('Category field not available for grouping');
    }

    // 按企业统计（如果存在companyName字段）
    let companyStats = [];
    try {
      companyStats = await (model as any).groupBy({
        by: ['companyName'],
        where: { ...where, isActive: true, companyName: { not: null } },
        _count: { companyName: true },
        orderBy: { _count: { companyName: 'desc' } },
        take: 10,
      });
    } catch (error) {
      console.log('CompanyName field not available for grouping');
    }

    // 按管理类别统计（如果存在managementType字段）
    let managementTypeStats = [];
    try {
      managementTypeStats = await (model as any).groupBy({
        by: ['managementType'],
        where: { ...where, isActive: true, managementType: { not: null } },
        _count: { managementType: true },
        orderBy: { _count: { managementType: 'desc' } },
      });
    } catch (error) {
      console.log('ManagementType field not available for grouping');
    }

    // 时间序列统计（如果存在approvalDate字段）
    let timeSeriesStats = [];
    try {
      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

      timeSeriesStats = await (model as any).groupBy({
        by: ['approvalDate'],
        where: {
          ...where,
          isActive: true,
          approvalDate: {
            gte: sixMonthsAgo,
            not: null
          }
        },
        _count: { approvalDate: true },
        orderBy: { approvalDate: 'desc' },
        take: 50,
      });

      // 按月份聚合
      const monthlyStats: Record<string, number> = {};
      timeSeriesStats.forEach((stat: any) => {
        if (stat.approvalDate) {
          const month = new Date(stat.approvalDate).toISOString().slice(0, 7); // YYYY-MM
          monthlyStats[month] = (monthlyStats[month] || 0) + stat._count.approvalDate;
        }
      });

      timeSeriesStats = Object.entries(monthlyStats)
        .map(([month, count]) => ({ month, count }))
        .sort((a, b) => a.month.localeCompare(b.month));
    } catch (error) {
      console.log('ApprovalDate field not available for time series');
    }

    return NextResponse.json({
      success: true,
      data: {
        basicStats: {
          totalCount,
          activeCount,
          inactiveCount: totalCount - activeCount,
        },
        categoryStats: categoryStats.map((stat: any) => ({
          name: stat.category,
          count: stat._count.category,
        })),
        companyStats: companyStats.map((stat: any) => ({
          name: stat.companyName,
          count: stat._count.companyName,
        })),
        managementTypeStats: managementTypeStats.map((stat: any) => ({
          name: stat.managementType,
          count: stat._count.managementType,
        })),
        timeSeriesStats,
      },
      appliedFilters: Object.fromEntries(searchParams.entries()),
      databaseInfo: {
        code: database,
        requiredLevel,
        totalFields: config.fields.length,
      }
    });

  } catch (error) {
    console.error('Stats API Error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch statistics' },
      { status: 500 }
    );
  }
} 
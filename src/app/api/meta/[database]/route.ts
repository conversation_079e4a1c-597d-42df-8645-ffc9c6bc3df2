import { NextResponse } from 'next/server';
import { getDatabaseConfig } from '@/lib/configCache';
import type { DatabaseConfig } from '@/lib/configCache';
import { getDynamicModel, validateDatabaseCode, isPrismaModel as isDynamicPrismaModel } from '@/lib/staticTableMappingService';

// 类型守卫：验证Prisma模型
function isPrismaModel(model: unknown): model is { findMany: (args: unknown) => unknown; groupBy: (args: unknown) => unknown } {
  return typeof model === 'object' && model !== null && 'findMany' in model && 'groupBy' in model;
}

export async function GET(
  request: Request,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const { database: rawDatabase } = await params;
    
    // 统一转换为小写格式
    const database = rawDatabase.toLowerCase();
    
    // 验证数据库代码
    const validation = await validateDatabaseCode(database);
    if (!validation.isValid) {
      return NextResponse.json(
        { success: false, error: validation.error },
        { status: validation.status || 400 }
      );
    }

    // 使用动态模型获取 - 重构版本：完全不依赖database字段
    const model = await getDynamicModel(database);
    if (!isDynamicPrismaModel(model)) {
      return NextResponse.json(
        { success: false, error: '模型未找到或无效' },
        { status: 500 }
      );
    }

    // 获取配置
    const config: DatabaseConfig = await getDatabaseConfig(database);
    // 只返回配置表中isFilterable的字段
    const metaFields = config.fields.filter(f => f.isFilterable);
    const metadata: Record<string, string[]> = {};
    const metadataWithCounts: Record<string, Array<{ value: string; count: number }>> = {};

    for (const fieldConfig of metaFields) {
      const field = fieldConfig.fieldName;
      
      // select和multi_select类型做distinct，date_range类型不需要distinct
      if (fieldConfig.filterType === 'select' || fieldConfig.filterType === 'multi_select') {
        try {
          // 重构版本：彻底移除database字段过滤逻辑
          // 每个数据库代码对应独立的表，不需要通过字段区分数据源
          const whereCondition: any = {
            // 移除 not: null 条件，允许包含空值
            // 只查询活跃记录
            // isActive: true, // 移除此硬编码条件以兼容没有该字段的模型
          };

          // 分别获取非空值和空值的计数
          // 1. 获取非空值的计数
          const groupedValues = await model.groupBy({
            by: [field as never],
            where: {
              ...whereCondition,
              [field]: { not: null, not: '' } // 排除null和空字符串
            },
            _count: {
              [field]: true,
            },
            orderBy: {
              [field]: 'asc',
            },
            take: 200, // 限制返回数量，避免内存问题
          }) as Array<Record<string, unknown> & { _count: Record<string, number> }>;

          // 2. 单独统计空值（null和空字符串）
          const nullCount = await model.count({
            where: {
              ...whereCondition,
              OR: [
                { [field]: null },
                { [field]: '' }
              ]
            }
          });

          // 3. 合并非空值和空值的结果
          const nonNullValues = groupedValues.map(item => ({
            value: String(item[field]).trim(),
            count: item._count[field] || 0,
            isNull: false
          }));

          // 4. 添加N/A项（如果有空值）
          const valuesWithCounts = [...nonNullValues];
          if (nullCount > 0) {
            valuesWithCounts.push({
              value: 'N/A',
              count: nullCount,
              isNull: true
            });
          }

          // 5. 排序：按计数倒序，N/A项排在最后
          valuesWithCounts.sort((a, b) => {
            if (a.isNull && !b.isNull) return 1;
            if (!a.isNull && b.isNull) return -1;
            return b.count - a.count; // 按计数倒序排列
          });

          // 保持向后兼容性，移除内部使用的isNull字段
          metadata[field] = valuesWithCounts.map(item => item.value);
          metadataWithCounts[field] = valuesWithCounts.map(item => ({
            value: item.value,
            count: item.count
          }));
          
        } catch (error) {
          console.error(`获取字段 ${field} 元数据失败:`, error);
          
          // 回退到简单查询方式
          try {
            const fallbackWhereCondition: any = {
              [field]: {
                not: null,
              },
              // isActive: true, // 移除此硬编码条件以兼容没有该字段的模型
            };
            
            const distinctValues = await model.findMany({
              select: {
                [field]: true,
              },
              distinct: [field],
              where: fallbackWhereCondition,
              orderBy: {
                [field]: 'asc',
              },
              take: 100,
            }) as Record<string, unknown>[];

            const values = distinctValues
              .map((item: Record<string, unknown>) => item[field])
              .filter((value: unknown): value is string | number => {
                if (typeof value === 'string') return true;
                if (typeof value === 'number' && !Number.isNaN(value)) return true;
                return false;
              })
              .map((value: string | number) => String(value).trim())
              .filter((value: string) => value !== '' && value.replace(/\s/g, '') !== '')
              .filter((value: string, index: number, self: string[]) => self.indexOf(value) === index);

            metadata[field] = values;
            metadataWithCounts[field] = values.map(value => ({ value, count: 0 }));
            
          } catch (fallbackError) {
            console.error(`字段 ${field} 回退查询也失败:`, fallbackError);
            // 对于出错的字段，返回空数组
            metadata[field] = [];
            metadataWithCounts[field] = [];
          }
        }
      } else if (fieldConfig.filterType === 'date_range') {
        // 日期区间类型不需要distinct，前端只需知道有此筛选项
        metadata[field] = [];
        metadataWithCounts[field] = [];
      }
    }

    return NextResponse.json({
      success: true,
      data: metadata,
      dataWithCounts: metadataWithCounts,
      config, // 返回当前使用的配置，便于前端调试
      databaseInfo: {
        code: database,
        originalCode: rawDatabase, // 保留原始代码用于调试
      }
    });
    
  } catch (error) {
    console.error(`元数据 API 错误:`, error);
    return NextResponse.json(
      { success: false, error: '内部服务器错误' },
      { status: 500 }
    );
  }
} 
'use client';

import { useState, useEffect } from 'react';
import { getDatabaseConfigs } from '@/lib/permissions';

export default function DebugPermissionsPage() {
  const [status, setStatus] = useState('初始化...');
  const [configs, setConfigs] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const testPermissions = async () => {
      try {
        setStatus('开始测试权限函数...');
        console.log('🔍 开始测试 getDatabaseConfigs...');
        
        const startTime = Date.now();
        const result = await getDatabaseConfigs();
        const endTime = Date.now();
        
        console.log('✅ getDatabaseConfigs 成功:', result);
        console.log(`⏱️ 耗时: ${endTime - startTime}ms`);
        
        setConfigs(result);
        setStatus(`成功获取配置，耗时 ${endTime - startTime}ms`);
        
      } catch (err) {
        console.error('❌ getDatabaseConfigs 失败:', err);
        setError(err instanceof Error ? err.message : '未知错误');
        setStatus('获取配置失败');
      }
    };

    // 延迟执行，确保组件完全挂载
    const timer = setTimeout(testPermissions, 100);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">权限函数调试页面</h1>
      
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">测试状态</h2>
        <p className="text-gray-600">{status}</p>
      </div>
      
      {error && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2 text-red-600">错误信息</h2>
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        </div>
      )}
      
      {configs && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">配置结果</h2>
          <p className="text-gray-600 mb-2">找到 {Object.keys(configs).length} 个数据库配置</p>
          
          <div className="space-y-4">
            {Object.entries(configs).map(([code, config]: [string, any]) => (
              <div key={code} className="border rounded-lg p-4 bg-gray-50">
                <h3 className="font-semibold text-lg">{config.name}</h3>
                <div className="mt-2 space-y-1 text-sm">
                  <p><strong>代码:</strong> {code}</p>
                  <p><strong>分类:</strong> {config.category}</p>
                  <p><strong>访问级别:</strong> {config.accessLevel}</p>
                  <p><strong>图标:</strong> {config.icon}</p>
                  {config.defaultSort && (
                    <p><strong>默认排序:</strong> {JSON.stringify(config.defaultSort)}</p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
      
      <div className="mt-8">
        <h2 className="text-xl font-semibold mb-2">直接 API 测试</h2>
        <button 
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          onClick={async () => {
            try {
              setStatus('测试直接 API 调用...');
              const response = await fetch('/api/config/databases');
              const result = await response.json();
              console.log('直接 API 结果:', result);
              setStatus('直接 API 调用成功');
            } catch (err) {
              console.error('直接 API 调用失败:', err);
              setStatus('直接 API 调用失败');
            }
          }}
        >
          测试直接 API 调用
        </button>
      </div>
    </div>
  );
}

"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import Navigation from "@/components/Navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import Link from "next/link";
import { useGlobalSearch } from "@/hooks/use-global-search";
import { getDatabaseConfigs } from "@/lib/permissions";

export default function HomePage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { query: searchQuery, setQuery: setSearchQuery, results } = useGlobalSearch();
  const [databaseConfigs, setDatabaseConfigs] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState(true);
  const [hasInitialized, setHasInitialized] = useState(false);

  // 获取数据库配置
  useEffect(() => {
    const fetchConfigs = async () => {
      try {
        const configs = await getDatabaseConfigs();
        setDatabaseConfigs(configs);
      } catch (error) {
        console.error('Failed to fetch database configs:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchConfigs();
  }, []);

  // 检查URL参数中是否有搜索查询（仅在初始加载时）
  useEffect(() => {
    const urlQuery = searchParams.get('q');
    if (urlQuery && urlQuery.trim() && !hasInitialized) {
      setSearchQuery(urlQuery.trim());
      setHasInitialized(true);
    } else if (!urlQuery && !hasInitialized) {
      setHasInitialized(true);
    }
  }, [searchParams, setSearchQuery, hasInitialized]);

  // 处理搜索操作
  const handleSearch = () => {
    if (searchQuery.trim()) {
      // 更新URL参数，触发搜索
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.set('q', searchQuery.trim());
      router.replace(newUrl.toString());
    }
  };

  // Home 页面搜索只负责展示下方结果，不自动导航
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      // prevent form submission / page reload
      e.preventDefault();
      handleSearch();
    }
  };

  // 根据动态获取的配置按类别分组
  const groupedDatabases = Object.entries(databaseConfigs).reduce<Record<string, { code: string; name: string }[]>>((acc, [code, cfg]) => {
    if (!acc[cfg.category]) acc[cfg.category] = [];
    acc[cfg.category].push({ code, name: cfg.name });
    return acc;
  }, {});

  // 将搜索结果转换为map便于快速查找
  const resultCountMap = new Map(results.map(r => [r.database.toLowerCase(), r.count]));

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />

      <main className="max-w-6xl mx-auto pt-16 pb-24">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold text-gray-900 mb-6">
            DataQuery Pharmaceutical Database
          </h1>

          {/* Search Area */}
          <div className="flex justify-center mb-20 px-4">
            <div className="flex items-center w-full max-w-2xl">
              <div className="relative flex-1">
                <Input
                  type="text"
                  placeholder="Enter product name, company name, application number, registration number, or title for search"
                  className="h-12 text-sm md:text-base pr-16 border-r-0 rounded-r-none"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={handleKeyPress}
                />
                <Search className="absolute right-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              </div>
              <Button
                className="h-12 px-4 md:px-8 rounded-l-none bg-blue-600 hover:bg-blue-700"
                disabled={!searchQuery.trim()}
                onClick={handleSearch}
              >
                Search
              </Button>
            </div>
          </div>
        </div>

        {/* 数据库目录（始终显示） */}
        <div className="px-4 md:px-8 space-y-6 max-w-4xl mx-auto">
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading database list...</p>
            </div>
          ) : Object.entries(groupedDatabases).map(([category, dbs]) => (
            <div key={category}>
              <div className="flex items-center mb-2 text-lg font-medium text-gray-800">
                <span className="mr-2">{category}</span>
              </div>
              <div className="flex flex-wrap gap-x-6 gap-y-2 pl-2">
                {dbs.map((d) => {
                  const count = searchQuery.trim() ? resultCountMap.get(d.code.toLowerCase()) ?? 0 : undefined;
                  return (
                    <Link
                      key={d.code}
                      href={searchQuery.trim() ? `/data/list/${d.code}?allFields=${encodeURIComponent(searchQuery)}` : `/data/list/${d.code}`}
                      className="text-sm text-blue-600 hover:underline flex items-center gap-1"
                    >
                      <span className="w-2 h-2 rounded-full bg-blue-500 inline-block" />
                      {d.name}
                      {count !== undefined ? ` (${count})` : null}
                    </Link>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-50 border-t border-gray-200 py-8">
        <div className="max-w-6xl mx-auto px-8">
          <div className="flex items-center justify-center space-x-6 text-sm text-gray-600">
            <Link href="/about" className="hover:text-blue-600">About Us</Link>
            <span>•</span>
            <Link href="/contact" className="hover:text-blue-600">Contact Us</Link>
            <span>•</span>
            <Link href="/sitemap" className="hover:text-blue-600">Site Map</Link>
          </div>
          <div className="text-center text-xs text-gray-500 mt-4">
            © 2025 DataQuery. All Rights Reserved.
          </div>
        </div>
      </footer>
    </div>
  );
}

'use client';

import { useState, useEffect } from 'react';

export default function SimpleTestPage() {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        console.log('🔍 开始简单测试...');
        
        const response = await fetch('/api/config/databases');
        console.log('📡 响应状态:', response.status);
        
        const result = await response.json();
        console.log('📋 响应数据:', result);
        
        setData(result);
      } catch (err) {
        console.error('❌ 错误:', err);
        setError(err instanceof Error ? err.message : '未知错误');
      } finally {
        setLoading(false);
      }
    };

    // 延迟执行，确保组件完全挂载
    const timer = setTimeout(fetchData, 100);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">简单测试页面</h1>
      
      {loading && (
        <div>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-2"></div>
          <p>加载中...</p>
        </div>
      )}
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <strong>错误:</strong> {error}
        </div>
      )}
      
      {!loading && !error && data && (
        <div>
          <h2 className="text-xl font-semibold mb-2">API 响应</h2>
          <pre className="bg-gray-100 p-4 rounded overflow-auto">
            {JSON.stringify(data, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}

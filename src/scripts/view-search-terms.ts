import { db } from '../lib/prisma';

// 解码URL编码的搜索词
function decodeSearchTerms(queryParams: string | null): string {
  if (!queryParams) return '无搜索词';
  
  try {
    const params = new URLSearchParams(queryParams);
    const searchTerms: string[] = [];
    
    // 检查各种搜索字段
    const searchFields = [
      { key: 'allFields', name: '综合搜索' },
      { key: 'productName', name: '产品名称' },
      { key: 'companyName', name: '公司名称' },
      { key: 'registrationNumber', name: '注册号' },
      { key: 'specification', name: '规格型号' },
      { key: 'category', name: '分类' }
    ];
    
    searchFields.forEach(field => {
      const value = params.get(field.key);
      if (value && value.trim()) {
        const decodedValue = decodeURIComponent(value);
        searchTerms.push(`${field.name}: "${decodedValue}"`);
      }
    });
    
    return searchTerms.length > 0 ? searchTerms.join(' | ') : '无有效搜索词';
  } catch (error) {
    return `解码失败: ${queryParams}`;
  }
}

async function viewActivityLogSearchTerms() {
  console.log('🔍 ActivityLog.queryParams Search Terms View\n');
  
  try {
    // 1. 获取有搜索词的ActivityLog记录
    const searchLogs = await db.activityLog.findMany({
      where: {
        queryParams: { not: null },
        eventType: {
          in: ['database_search', 'advanced_search', 'filter']
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 30,
      select: {
        id: true,
        eventType: true,
        database: true,
        queryParams: true,
        createdAt: true,
        userId: true,
        ip: true
      }
    });

    console.log(`找到 ${searchLogs.length} 条包含搜索词的记录:\n`);
    
    // 2. 显示解码后的搜索词
    searchLogs.forEach((log, index) => {
      const decodedTerms = decodeSearchTerms(log.queryParams);
      console.log(`${index + 1}. [${log.createdAt.toLocaleString('zh-CN')}]`);
      console.log(`   事件类型: ${log.eventType}`);
      console.log(`   数据库: ${log.database || '未知'}`);
      console.log(`   用户: ${log.userId || '匿名'} | IP: ${log.ip}`);
      console.log(`   原始参数: ${log.queryParams}`);
      console.log(`   解码搜索词: ${decodedTerms}`);
      console.log('');
    });

    // 3. 统计搜索词频率
    console.log('📊 搜索词频率统计:');
    const termFrequency: Record<string, number> = {};
    
    searchLogs.forEach(log => {
      if (log.queryParams) {
        try {
          const params = new URLSearchParams(log.queryParams);
          ['allFields', 'productName', 'companyName', 'registrationNumber'].forEach(field => {
            const value = params.get(field);
            if (value && value.trim()) {
              const decodedValue = decodeURIComponent(value);
              termFrequency[decodedValue] = (termFrequency[decodedValue] || 0) + 1;
            }
          });
        } catch (error) {
          // 忽略解码错误
        }
      }
    });

    const sortedTerms = Object.entries(termFrequency)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 15);

    sortedTerms.forEach(([term, count], index) => {
      console.log(`${index + 1}. "${term}": ${count}次`);
    });

    // 4. 按数据库统计
    console.log('\n📈 按数据库统计搜索:');
    const dbStats = await db.activityLog.groupBy({
      by: ['database'],
      where: {
        queryParams: { not: null },
        database: { not: null }
      },
      _count: { database: true },
      orderBy: { _count: { database: 'desc' } }
    });

    dbStats.forEach((stat, index) => {
      console.log(`${index + 1}. ${stat.database}: ${stat._count.database}次搜索`);
    });

  } catch (error) {
    console.error('查询ActivityLog失败:', error);
  }
}

async function viewSearchAnalyticsTerms() {
  console.log('\n🔍 SearchAnalytics.searchQuery 搜索词查看\n');
  
  try {
    // 1. 获取SearchAnalytics记录
    const searchAnalytics = await db.searchAnalytics.findMany({
      orderBy: { createdAt: 'desc' },
      take: 20,
      select: {
        id: true,
        database: true,
        searchType: true,
        searchQuery: true,
        searchFields: true,
        filters: true,
        resultsCount: true,
        searchTime: true,
        createdAt: true,
        userId: true
      }
    });

    console.log(`找到 ${searchAnalytics.length} 条搜索分析记录:\n`);
    
    // 2. 显示详细搜索信息
    searchAnalytics.forEach((record, index) => {
      console.log(`${index + 1}. [${record.createdAt.toLocaleString('zh-CN')}]`);
      console.log(`   搜索类型: ${record.searchType}`);
      console.log(`   数据库: ${record.database}`);
      console.log(`   搜索词: "${record.searchQuery || '无'}"`);
      
      // 解析搜索字段
      if (record.searchFields) {
        try {
          const fields = JSON.parse(record.searchFields as string);
          const fieldInfo = Object.entries(fields)
            .filter(([, value]) => value && String(value).trim())
            .map(([key, value]) => `${key}: "${value}"`)
            .join(', ');
          console.log(`   搜索字段: ${fieldInfo || '无'}`);
        } catch (e) {
          console.log(`   搜索字段: 解析失败`);
        }
      }
      
      // 解析筛选条件
      if (record.filters) {
        try {
          const filters = JSON.parse(record.filters as string);
          const filterInfo = Object.entries(filters)
            .filter(([, value]) => value && String(value).trim())
            .map(([key, value]) => `${key}: "${value}"`)
            .join(', ');
          console.log(`   Filter conditions: ${filterInfo || 'None'}`);
        } catch (e) {
          console.log(`   Filter conditions: Parse failed`);
        }
      }
      
      console.log(`   Result count: ${record.resultsCount || 0} records`);
      console.log(`   Search time: ${record.searchTime || 0}ms`);
      console.log(`   User: ${record.userId || 'Anonymous'}`);
      console.log('');
    });

    // 3. 统计热门搜索词
    console.log('📊 Popular Search Terms Statistics:');
    const queryStats = await db.searchAnalytics.groupBy({
      by: ['searchQuery'],
      where: { searchQuery: { not: null } },
      _count: { searchQuery: true },
      orderBy: { _count: { searchQuery: 'desc' } },
      take: 15
    });

    queryStats.forEach((stat, index) => {
      console.log(`${index + 1}. "${stat.searchQuery}": ${stat._count.searchQuery}次`);
    });

    // 4. 搜索类型统计
    console.log('\n📈 搜索类型统计:');
    const typeStats = await db.searchAnalytics.groupBy({
      by: ['searchType'],
      _count: { searchType: true },
      orderBy: { _count: { searchType: 'desc' } }
    });

    typeStats.forEach((stat, index) => {
      console.log(`${index + 1}. ${stat.searchType}: ${stat._count.searchType}次`);
    });

  } catch (error) {
    console.error('查询SearchAnalytics失败:', error);
  }
}

async function main() {
  console.log('🔍 搜索词数据查看工具\n');
  console.log('=' .repeat(60));
  
  await viewActivityLogSearchTerms();
  
  console.log('\n' + '=' .repeat(60));
  
  await viewSearchAnalyticsTerms();
  
  console.log('\n✅ 查看完成!');
}

// 如果直接运行此脚本
if (require.main === module) {
  main()
    .catch(error => {
      console.error('脚本执行失败:', error);
      process.exit(1);
    })
    .finally(() => {
      process.exit(0);
    });
}

export { viewActivityLogSearchTerms, viewSearchAnalyticsTerms };

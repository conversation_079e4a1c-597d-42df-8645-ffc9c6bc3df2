import { db } from '../lib/prisma';
import * as fs from 'fs';
import * as path from 'path';
import * as XLSX from 'xlsx';
import { getTableConfig, validateTableConfig, ImportMode, TableConfig } from '../lib/uniqueKeyConfig';
import { SmartSyncEngine, SyncResult } from '../lib/syncEngine';

// 通用数据转换函数（与CSV导入保持一致）
function transformDataByConfig(data: any, tableConfig: TableConfig, databaseType: string): Record<string, any> {
  const transformedData: Record<string, any> = {};
  
  // 获取字段映射配置
  const fieldMapping = tableConfig.fieldMapping || {};
  
  // 遍历所有可能的字段映射
  for (const [sourceField, targetField] of Object.entries(fieldMapping)) {
    // 支持多种字段名格式：英文、中文、或其他自定义格式
    const value = data[targetField] || data[sourceField] || data[sourceField.toLowerCase()] || data[sourceField.toUpperCase()];
    if (value !== undefined) {
      transformedData[targetField] = value;
    }
  }
  
  // 添加数据库类型字段
  transformedData.database = databaseType;
  
  return transformedData;
}

function importExcelData(excelFilePath: string, databaseType: string, tableConfig: TableConfig, sheetName?: string): Record<string, any>[] {
  // 读取Excel文件
  const workbook = XLSX.readFile(excelFilePath);
  
  // 获取工作表
  const sheet = sheetName ? workbook.Sheets[sheetName] : workbook.Sheets[workbook.SheetNames[0]];
  
  if (!sheet) {
    throw new Error(`工作表不存在: ${sheetName || workbook.SheetNames[0]}`);
  }
  
  // 转换为JSON
  const jsonData = XLSX.utils.sheet_to_json(sheet);
  
  // 使用通用转换函数处理每一行数据
  return jsonData.map((row: any) => transformDataByConfig(row, tableConfig, databaseType));
}

// 打印同步结果
function printSyncResult(result: SyncResult, mode: string) {
  console.log('\n📊 同步结果统计:');
  console.log(`  导入模式: ${mode}`);
  console.log(`  总记录数: ${result.totalRecords}`);
  console.log(`  新增记录: ${result.inserted}`);
  console.log(`  更新记录: ${result.updated}`);
  console.log(`  跳过记录: ${result.skipped}`);
  console.log(`  错误记录: ${result.errors}`);
  console.log(`  处理时间: ${result.processingTime}ms`);
  
  if (result.errorDetails.length > 0) {
    console.log('\n❌ 错误详情:');
    result.errorDetails.forEach((error, index) => {
      console.log(`  ${index + 1}. 第${error.row}行 - ${error.businessKey}: ${error.error}`);
    });
  }
  
  if (result.inserted > 0 || result.updated > 0) {
    console.log('\n✅ 数据同步完成！');
  } else if (result.errors === 0) {
    console.log('\nℹ️ 没有新数据需要同步');
  }
}

async function main() {
  const excelFilePath = process.argv[2];
  const databaseType = process.argv[3];
  const tableName = process.argv[4] || 'MedicalDevice'; // 支持指定表名，默认为MedicalDevice
  const sheetName = process.argv[5]; // 可选参数
  const importMode = process.argv[6] as ImportMode || 'upsert'; // 默认使用upsert模式

  if (!excelFilePath || !databaseType) {
    console.error('使用方法: npm run import-excel <excel文件路径> <数据库类型> [表名] [工作表名称] [导入模式]');
    console.error('参数说明:');
    console.error('  excel文件路径: Excel文件的完整路径');
    console.error('  数据库类型: 目标数据库类型标识');
    console.error('  表名: 可选，默认为MedicalDevice');
    console.error('  工作表名称: 可选，默认为第一个工作表');
    console.error('  导入模式: 可选，insert(仅插入) | upsert(智能同步) | replace(全量替换)');
    console.error('');
    console.error('示例:');
    console.error('  npm run import-excel ./data/devices.xlsx deviceCNImported');
    console.error('  npm run import-excel ./data/devices.xlsx deviceCNImported MedicalDevice "Sheet1" upsert');
    console.error('  npm run import-excel ./data/companies.xlsx companyData Company "Companies" insert');
    process.exit(1);
  }

  if (!fs.existsSync(excelFilePath)) {
    console.error(`Excel文件不存在: ${excelFilePath}`);
    process.exit(1);
  }

  if (!validateTableConfig(tableName)) {
    console.error(`不支持的表: ${tableName}`);
    const supportedTables = ['MedicalDevice']; // 这里可以动态获取支持的表
    console.error(`支持的表: ${supportedTables.join(', ')}`);
    console.error('');
    console.error('如需添加新表支持，请在 src/lib/uniqueKeyConfig.ts 中添加相应配置');
    process.exit(1);
  }

  const tableConfig = getTableConfig(tableName);
  if (!tableConfig) {
    console.error(`表配置不存在: ${tableName}`);
    process.exit(1);
  }

  try {
    console.log(`📁 开始导入Excel文件: ${excelFilePath}`);
    console.log(`🗄️ 目标数据库: ${databaseType}`);
    console.log(`📋 目标表: ${tableName}`);
    console.log(`🔄 导入模式: ${importMode}`);
    if (sheetName) {
      console.log(`📋 工作表: ${sheetName}`);
    }
    console.log(`📝 表描述: ${tableConfig.description || '无'}`);
    
    // 显示字段映射信息
    if (tableConfig.fieldMapping) {
      console.log(`🔗 字段映射配置:`);
      Object.entries(tableConfig.fieldMapping).forEach(([source, target]) => {
        console.log(`   ${source} → ${target}`);
      });
    }

    const records = importExcelData(excelFilePath, databaseType, tableConfig, sheetName);
    console.log(`📊 读取到 ${records.length} 条记录`);

    if (records.length === 0) {
      console.log('❌ Excel文件中没有数据');
      process.exit(1);
    }

    // 显示数据样本
    console.log('\n📋 数据样本:');
    const sampleRecord = records[0];
    Object.entries(sampleRecord).slice(0, 5).forEach(([key, value]) => {
      console.log(`   ${key}: ${value}`);
    });
    if (Object.keys(sampleRecord).length > 5) {
      console.log(`   ... 还有 ${Object.keys(sampleRecord).length - 5} 个字段`);
    }

    // 创建智能同步引擎
    const syncEngine = new SmartSyncEngine(
      tableName, 
      'EXCEL_IMPORT', 
      path.basename(excelFilePath)
    );
    syncEngine.setTableConfig(tableConfig);

    console.log('\n🚀 开始智能同步...');
    const result = await syncEngine.syncData(records, importMode);
    
    // 打印结果
    printSyncResult(result, importMode);

  } catch (error) {
    console.error('❌ 导入失败:', error);
    process.exit(1);
  } finally {
    await db.$disconnect();
  }
}

main(); 
import { db } from '../lib/prisma';

async function cleanupLogs() {
  console.log('🧹 开始清理旧的活动日志...');

  try {
    // 保留最近90天的日志
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - 90);

    console.log(`📅 删除 ${cutoffDate.toISOString()} 之前的日志`);

    // 统计要删除的记录数
    const countToDelete = await db.activityLog.count({
      where: {
        createdAt: {
          lt: cutoffDate,
        },
      },
    });

    console.log(`📊 找到 ${countToDelete} 条旧日志记录`);

    if (countToDelete === 0) {
      console.log('✅ 没有需要清理的日志');
      return;
    }

    // 分批删除，避免一次性删除太多记录
    const batchSize = 1000;
    let deletedCount = 0;

    while (deletedCount < countToDelete) {
      const batch = await db.activityLog.deleteMany({
        where: {
          createdAt: {
            lt: cutoffDate,
          },
        },
        // 注意：Prisma 的 deleteMany 不支持 take/skip
        // 所以我们需要重复执行直到没有更多记录
      });

      deletedCount += batch.count;
      console.log(`🗑️  已删除 ${deletedCount}/${countToDelete} 条记录`);

      // 如果这批没有删除任何记录，说明已经完成
      if (batch.count === 0) {
        break;
      }

      // 短暂暂停，避免对数据库造成过大压力
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    console.log(`✅ 清理完成！总共删除了 ${deletedCount} 条旧日志记录`);

    // 显示清理后的统计信息
    const remainingCount = await db.activityLog.count();
    console.log(`📈 剩余日志记录: ${remainingCount} 条`);

    // 显示最早的记录日期
    const oldestLog = await db.activityLog.findFirst({
      orderBy: {
        createdAt: 'asc',
      },
      select: {
        createdAt: true,
      },
    });

    if (oldestLog) {
      console.log(`📅 最早记录日期: ${oldestLog.createdAt.toISOString()}`);
    }

  } catch (error) {
    console.error('❌ 清理日志时发生错误:', error);
    process.exit(1);
  } finally {
    await db.$disconnect();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  cleanupLogs().catch(error => {
    console.error('脚本执行失败:', error);
    process.exit(1);
  });
}

export { cleanupLogs };

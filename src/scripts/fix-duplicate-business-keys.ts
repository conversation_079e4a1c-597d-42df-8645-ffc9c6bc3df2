import { db } from '../lib/prisma';
import { generateBusinessKeyHash, isValidHash } from '../lib/utils';
import { getTableConfig, isHashEnabled, getHashAlgorithm } from '../lib/uniqueKeyConfig';

interface MigrationResult {
  totalRecords: number;
  updatedRecords: number;
  skippedRecords: number;
  errorRecords: number;
  processingTime: number;
  errors: Array<{
    id: string;
    businessKey: string;
    error: string;
  }>;
}

/**
 * 为已有数据生成Hash值
 */
async function migrateBusinessKeyHashes(tableName: string): Promise<MigrationResult> {
  const startTime = Date.now();
  const result: MigrationResult = {
    totalRecords: 0,
    updatedRecords: 0,
    skippedRecords: 0,
    errorRecords: 0,
    processingTime: 0,
    errors: [],
  };

  try {
    // 检查表配置
    if (!isHashEnabled(tableName)) {
      console.log(`表 ${tableName} 未启用Hash模式，跳过迁移`);
      return result;
    }

    const algorithm = getHashAlgorithm(tableName);
    if (algorithm === 'none') {
      console.log(`表 ${tableName} 未配置Hash算法，跳过迁移`);
      return result;
    }

    console.log(`开始为表 ${tableName} 生成Hash值，使用算法: ${algorithm}`);

    // 获取所有需要处理的记录
    const records = await db.medicalDevice.findMany({
      where: {
        OR: [
          { businessKeyHash: null },
          { businessKeyHash: '' }
        ]
      },
      select: {
        id: true,
        businessKey: true,
        businessKeyHash: true,
      }
    });

    result.totalRecords = records.length;
    console.log(`找到 ${records.length} 条需要生成Hash的记录`);

    if (records.length === 0) {
      console.log('没有需要处理的记录');
      return result;
    }

    // 批量处理记录
    const batchSize = 100; // 每批处理100条记录
    for (let i = 0; i < records.length; i += batchSize) {
      const batch = records.slice(i, i + batchSize);
      
      console.log(`处理第 ${Math.floor(i / batchSize) + 1} 批，共 ${batch.length} 条记录`);
      
      for (const record of batch) {
        try {
          // 检查是否已有有效的Hash值
          if (record.businessKeyHash && isValidHash(record.businessKeyHash, algorithm)) {
            result.skippedRecords++;
            continue;
          }

          // 生成Hash值
          const hash = generateBusinessKeyHash(record.businessKey, algorithm);
          
          // 更新记录
          await db.medicalDevice.update({
            where: { id: record.id },
            data: { businessKeyHash: hash }
          });

          result.updatedRecords++;

        } catch (error) {
          result.errorRecords++;
          result.errors.push({
            id: record.id,
            businessKey: record.businessKey,
            error: error instanceof Error ? error.message : String(error)
          });
        }
      }

      // 每批处理后暂停一下，避免数据库压力过大
      if (i + batchSize < records.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

  } catch (error) {
    console.error('迁移过程中发生错误:', error);
    result.errors.push({
      id: 'SYSTEM_ERROR',
      businessKey: 'N/A',
      error: error instanceof Error ? error.message : String(error)
    });
  }

  result.processingTime = Date.now() - startTime;
  return result;
}

/**
 * 检查并修复重复的Hash值
 */
async function fixDuplicateHashes(tableName: string): Promise<MigrationResult> {
  const startTime = Date.now();
  const result: MigrationResult = {
    totalRecords: 0,
    updatedRecords: 0,
    skippedRecords: 0,
    errorRecords: 0,
    processingTime: 0,
    errors: [],
  };

  try {
    console.log(`检查表 ${tableName} 的重复Hash值`);

    // 查找重复的Hash值
    const duplicates = await db.$queryRaw<Array<{ businessKeyHash: string; count: number }>>`
      SELECT "businessKeyHash", COUNT(*) as count
      FROM "MedicalDevice"
      WHERE "businessKeyHash" IS NOT NULL
      GROUP BY "businessKeyHash"
      HAVING COUNT(*) > 1
      ORDER BY count DESC
    `;

    if (duplicates.length === 0) {
      console.log('没有发现重复的Hash值');
      return result;
    }

    console.log(`发现 ${duplicates.length} 个重复的Hash值`);

    for (const duplicate of duplicates) {
      console.log(`处理重复Hash: ${duplicate.businessKeyHash} (${duplicate.count} 条记录)`);
      
      // 获取所有使用该Hash值的记录
      const records = await db.medicalDevice.findMany({
        where: { businessKeyHash: duplicate.businessKeyHash },
        orderBy: { createdAt: 'asc' }
      });

      // 保留第一条记录，为其他记录重新生成Hash值
      for (let i = 1; i < records.length; i++) {
        try {
          const record = records[i];
          const algorithm = getHashAlgorithm(tableName);
          
          // 在原始businessKey后添加时间戳，确保唯一性
          const uniqueKey = `${record.businessKey}_${Date.now()}_${i}`;
          const newHash = generateBusinessKeyHash(uniqueKey, algorithm);
          
          await db.medicalDevice.update({
            where: { id: record.id },
            data: { 
              businessKeyHash: newHash,
              notes: record.notes ? `${record.notes} [Hash冲突修复]` : '[Hash冲突修复]'
            }
          });

          result.updatedRecords++;
          console.log(`  修复记录 ${record.id}: ${duplicate.businessKeyHash} -> ${newHash}`);

        } catch (error) {
          result.errorRecords++;
          result.errors.push({
            id: records[i].id,
            businessKey: records[i].businessKey,
            error: error instanceof Error ? error.message : String(error)
          });
        }
      }
    }

  } catch (error) {
    console.error('修复重复Hash时发生错误:', error);
    result.errors.push({
      id: 'SYSTEM_ERROR',
      businessKey: 'N/A',
      error: error instanceof Error ? error.message : String(error)
    });
  }

  result.processingTime = Date.now() - startTime;
  return result;
}

/**
 * 验证Hash值的完整性
 */
async function validateHashIntegrity(tableName: string): Promise<void> {
  try {
    console.log(`验证表 ${tableName} 的Hash完整性`);

    const algorithm = getHashAlgorithm(tableName);
    
    // 检查是否有记录缺少Hash值
    const missingHashCount = await db.medicalDevice.count({
      where: {
        OR: [
          { businessKeyHash: null },
          { businessKeyHash: '' }
        ]
      }
    });

    if (missingHashCount > 0) {
      console.log(`⚠️  发现 ${missingHashCount} 条记录缺少Hash值`);
    } else {
      console.log('✅ 所有记录都有Hash值');
    }

    // 检查Hash值格式是否正确
    const invalidHashCount = await db.medicalDevice.count({
      where: {
        businessKeyHash: {
          not: null
        }
      }
    });

    if (invalidHashCount > 0) {
      console.log(`📊 共有 ${invalidHashCount} 条记录包含Hash值`);
    }

    // 检查重复Hash值
    const duplicateHashCount = await db.$queryRaw<Array<{ count: number }>>`
      SELECT COUNT(*) as count
      FROM (
        SELECT "businessKeyHash"
        FROM "MedicalDevice"
        WHERE "businessKeyHash" IS NOT NULL
        GROUP BY "businessKeyHash"
        HAVING COUNT(*) > 1
      ) as duplicates
    `;

    if (duplicateHashCount[0]?.count > 0) {
      console.log(`❌ 发现 ${duplicateHashCount[0].count} 个重复的Hash值`);
    } else {
      console.log('✅ 没有重复的Hash值');
    }

  } catch (error) {
    console.error('验证Hash完整性时发生错误:', error);
  }
}

// 主函数
async function main() {
  const command = process.argv[2];
  const tableName = process.argv[3] || 'MedicalDevice';

  if (!command) {
    console.log('数据迁移工具使用说明:');
    console.log('  npm run migrate-hash generate [表名]    - 为已有数据生成Hash值');
    console.log('  npm run migrate-hash fix [表名]        - 修复重复的Hash值');
    console.log('  npm run migrate-hash validate [表名]   - 验证Hash完整性');
    console.log('  npm run migrate-hash all [表名]        - 执行完整的Hash迁移流程');
    process.exit(1);
  }

  try {
    switch (command) {
      case 'generate':
        const generateResult = await migrateBusinessKeyHashes(tableName);
        console.log('\n📊 生成Hash结果:');
        console.log(`  总记录数: ${generateResult.totalRecords}`);
        console.log(`  更新记录: ${generateResult.updatedRecords}`);
        console.log(`  跳过记录: ${generateResult.skippedRecords}`);
        console.log(`  错误记录: ${generateResult.errorRecords}`);
        console.log(`  处理时间: ${generateResult.processingTime}ms`);
        
        if (generateResult.errors.length > 0) {
          console.log('\n❌ 错误详情:');
          generateResult.errors.forEach((error, index) => {
            console.log(`  ${index + 1}. ${error.businessKey}: ${error.error}`);
          });
        }
        break;

      case 'fix':
        const fixResult = await fixDuplicateHashes(tableName);
        console.log('\n📊 修复重复Hash结果:');
        console.log(`  更新记录: ${fixResult.updatedRecords}`);
        console.log(`  错误记录: ${fixResult.errorRecords}`);
        console.log(`  处理时间: ${fixResult.processingTime}ms`);
        break;

      case 'validate':
        await validateHashIntegrity(tableName);
        break;

      case 'all':
        console.log('🚀 开始完整的Hash迁移流程...');
        
        console.log('\n1️⃣ 生成Hash值...');
        const generateResultAll = await migrateBusinessKeyHashes(tableName);
        console.log(`   完成: ${generateResultAll.updatedRecords} 条记录更新`);
        
        console.log('\n2️⃣ 修复重复Hash值...');
        const fixResultAll = await fixDuplicateHashes(tableName);
        console.log(`   完成: ${fixResultAll.updatedRecords} 条记录修复`);
        
        console.log('\n3️⃣ 验证Hash完整性...');
        await validateHashIntegrity(tableName);
        
        console.log('\n✅ Hash迁移流程完成！');
        break;

      default:
        console.error(`未知命令: ${command}`);
        process.exit(1);
    }

  } catch (error) {
    console.error('执行过程中发生错误:', error);
    process.exit(1);
  } finally {
    await db.$disconnect();
  }
}

main(); 
import { db } from '../lib/prisma';

async function main() {
  // 统计总访问量
  const totalVisits = await db.activityLog.count();

  // 统计独立IP数
  const uniqueIpCount = await (db.activityLog as any).findMany({
    distinct: ['ip'],
    select: { ip: true },
  });

  // 统计热门数据库
  const dbStats = await (db.activityLog as any).groupBy({
    by: ['database'],
    _count: { database: true },
    where: { database: { not: null } },
    orderBy: { _count: { database: 'desc' } },
  });

  // 统计跳出率（只看pageview事件，sessionId唯一且只出现一次视为跳出）
  const pageviews = await (db.activityLog as any).findMany({
    where: { eventType: 'pageview', sessionId: { not: null } },
    select: { sessionId: true },
  });
  const sessionMap: Record<string, number> = {};
  for (const { sessionId } of pageviews) {
    if (sessionId) sessionMap[sessionId] = (sessionMap[sessionId] || 0) + 1;
  }
  const totalSessions = Object.keys(sessionMap).length;
  const bouncedSessions = Object.values(sessionMap).filter(count => count === 1).length;
  const bounceRate = totalSessions > 0 ? (bouncedSessions / totalSessions) * 100 : 0;

  // 输出报表
  console.log('--- Website Traffic Analysis Report ---');
  console.log('Total visits:', totalVisits);
  console.log('Unique IPs:', uniqueIpCount.length);
  console.log('Popular databases:');
  dbStats.forEach((stat: any) => {
    console.log(`  Database: ${stat.database || 'Unknown'} Visits: ${stat._count?.database}`);
  });
  console.log('Bounce rate:', bounceRate.toFixed(2) + '%');
}

main().catch(e => {
  console.error(e);
  process.exit(1);
}); 
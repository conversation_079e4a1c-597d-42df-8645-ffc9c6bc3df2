"use client";

import { useState, useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { RefreshCw } from "lucide-react";

interface CaptchaProps {
  value: string;
  onChange: (value: string) => void;
  onValidate: (isValid: boolean) => void;
  className?: string;
}

export function Captcha({ value, onChange, onValidate, className = "" }: CaptchaProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [captchaCode, setCaptchaCode] = useState("");
  const [isValidating, setIsValidating] = useState(false);

  // Generate random string with mixed case letters and numbers
  const generateCaptchaCode = () => {
    const chars = "ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789";
    let result = "";
    for (let i = 0; i < 6; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  };

  // Draw captcha on canvas with interference
  const drawCaptcha = (code: string) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Background with gradient
    const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
    gradient.addColorStop(0, "#f8f9fa");
    gradient.addColorStop(1, "#e9ecef");
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Add noise dots
    for (let i = 0; i < 50; i++) {
      ctx.fillStyle = `rgba(${Math.random() * 255}, ${Math.random() * 255}, ${Math.random() * 255}, 0.3)`;
      ctx.beginPath();
      ctx.arc(
        Math.random() * canvas.width,
        Math.random() * canvas.height,
        Math.random() * 2,
        0,
        2 * Math.PI
      );
      ctx.fill();
    }

    // Add interference lines
    for (let i = 0; i < 5; i++) {
      ctx.strokeStyle = `rgba(${Math.random() * 255}, ${Math.random() * 255}, ${Math.random() * 255}, 0.4)`;
      ctx.lineWidth = Math.random() * 2 + 1;
      ctx.beginPath();
      ctx.moveTo(Math.random() * canvas.width, Math.random() * canvas.height);
      ctx.lineTo(Math.random() * canvas.width, Math.random() * canvas.height);
      ctx.stroke();
    }

    // Draw captcha text with random colors and positions
    const fontSize = 28;
    ctx.font = `bold ${fontSize}px Arial, sans-serif`;
    ctx.textBaseline = "middle";

    for (let i = 0; i < code.length; i++) {
      const char = code[i];
      const x = 20 + i * 25 + Math.random() * 10 - 5;
      const y = canvas.height / 2 + Math.random() * 10 - 5;
      
      // Random rotation
      const angle = (Math.random() - 0.5) * 0.4;
      ctx.save();
      ctx.translate(x, y);
      ctx.rotate(angle);
      
      // Random color
      const colors = ["#2563eb", "#dc2626", "#059669", "#7c2d12", "#4338ca", "#be185d"];
      ctx.fillStyle = colors[Math.floor(Math.random() * colors.length)];
      
      // Add shadow
      ctx.shadowColor = "rgba(0,0,0,0.3)";
      ctx.shadowBlur = 2;
      ctx.shadowOffsetX = 1;
      ctx.shadowOffsetY = 1;
      
      ctx.fillText(char, 0, 0);
      ctx.restore();
    }

    // Add more interference lines on top
    for (let i = 0; i < 3; i++) {
      ctx.strokeStyle = `rgba(${Math.random() * 255}, ${Math.random() * 255}, ${Math.random() * 255}, 0.2)`;
      ctx.lineWidth = Math.random() * 1 + 0.5;
      ctx.beginPath();
      ctx.moveTo(0, Math.random() * canvas.height);
      ctx.lineTo(canvas.width, Math.random() * canvas.height);
      ctx.stroke();
    }
  };

  // Generate new captcha
  const refreshCaptcha = () => {
    const newCode = generateCaptchaCode();
    setCaptchaCode(newCode);
    drawCaptcha(newCode);
    onChange("");
    onValidate(false);
  };

  // Validate input
  useEffect(() => {
    if (value.length === 6) {
      setIsValidating(true);
      // Add slight delay to simulate validation
      const timer = setTimeout(() => {
        const isValid = value.toLowerCase() === captchaCode.toLowerCase();
        onValidate(isValid);
        setIsValidating(false);
      }, 300);
      return () => clearTimeout(timer);
    } else {
      onValidate(false);
    }
  }, [value, captchaCode, onValidate]);

  // Initialize captcha on mount
  useEffect(() => {
    refreshCaptcha();
  }, []);

  return (
    <div className={`space-y-3 ${className}`}>
      <div className="flex items-center space-x-3">
        <div className="relative">
          <canvas
            ref={canvasRef}
            width={180}
            height={60}
            className="border border-gray-300 rounded-md bg-white cursor-pointer hover:border-gray-400 transition-colors"
            onClick={refreshCaptcha}
            title="Click to refresh captcha"
          />
          {isValidating && (
            <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-md">
              <RefreshCw className="h-4 w-4 animate-spin text-blue-600" />
            </div>
          )}
        </div>
        
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={refreshCaptcha}
          className="flex items-center space-x-1"
        >
          <RefreshCw className="h-3 w-3" />
          <span className="text-xs">Refresh</span>
        </Button>
      </div>

      <div className="space-y-1">
        <Input
          value={value}
          onChange={(e) => onChange(e.target.value.slice(0, 6))}
          placeholder="Enter the code above"
          className="w-full text-sm"
          maxLength={6}
          autoComplete="off"
        />
        <p className="text-xs text-gray-500">
          Enter the 6-character code shown in the image above (case-insensitive)
        </p>
      </div>
    </div>
  );
}

// Alternative: Slider Captcha Component
interface SliderCaptchaProps {
  onValidate: (isValid: boolean) => void;
  className?: string;
}

export function SliderCaptcha({ onValidate, className = "" }: SliderCaptchaProps) {
  const [sliderPosition, setSliderPosition] = useState(0);
  const [targetPosition, setTargetPosition] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [isValidated, setIsValidated] = useState(false);
  const sliderRef = useRef<HTMLDivElement>(null);

  const generateTarget = () => {
    const newTarget = Math.floor(Math.random() * 200) + 50; // 50-250px range
    setTargetPosition(newTarget);
    setSliderPosition(0);
    setIsValidated(false);
    onValidate(false);
  };

  useEffect(() => {
    generateTarget();
  }, []);

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    e.preventDefault();
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging || !sliderRef.current) return;

    const rect = sliderRef.current.getBoundingClientRect();
    const newPosition = Math.max(0, Math.min(300, e.clientX - rect.left));
    setSliderPosition(newPosition);

    // Check if close to target (within 10px tolerance)
    const tolerance = 15;
    if (Math.abs(newPosition - targetPosition) < tolerance && !isValidated) {
      setIsValidated(true);
      onValidate(true);
    } else if (isValidated && Math.abs(newPosition - targetPosition) >= tolerance) {
      setIsValidated(false);
      onValidate(false);
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, targetPosition, isValidated]);

  return (
    <div className={`space-y-3 ${className}`}>
      <div className="space-y-2">
        <p className="text-sm font-medium text-gray-700">
          Drag the slider to the highlighted position
        </p>
        
        <div
          ref={sliderRef}
          className="relative w-80 h-12 bg-gray-200 rounded-md border border-gray-300 overflow-hidden"
        >
          {/* Target zone */}
          <div
            className="absolute top-0 bottom-0 w-8 bg-blue-200 border-2 border-blue-400 rounded"
            style={{ left: `${targetPosition}px` }}
          />
          
          {/* Slider track */}
          <div
            className="absolute top-0 bottom-0 bg-blue-500 rounded-l"
            style={{ width: `${sliderPosition}px` }}
          />
          
          {/* Slider handle */}
          <div
            className={`absolute top-1 bottom-1 w-8 bg-white border-2 rounded cursor-pointer shadow-md transition-colors ${
              isValidated ? 'border-green-500 bg-green-50' : 'border-gray-400'
            } ${isDragging ? 'border-blue-500' : ''}`}
            style={{ left: `${Math.max(0, sliderPosition - 16)}px` }}
            onMouseDown={handleMouseDown}
          >
            <div className="flex items-center justify-center h-full">
              <div className="w-1 h-4 bg-gray-400 rounded"></div>
              <div className="w-1 h-4 bg-gray-400 rounded ml-1"></div>
            </div>
          </div>
        </div>
        
        <div className="flex justify-between items-center">
          <p className="text-xs text-gray-500">
            {isValidated ? "✓ Verification successful" : "Drag the slider to match the target"}
          </p>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={generateTarget}
            className="text-xs"
          >
            Reset
          </Button>
        </div>
      </div>
    </div>
  );
}

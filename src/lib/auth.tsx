"use client";

import {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from 'react';
import type { MembershipType } from './permissions';

// 用户信息类型定义
// 注意：这个类型应该与后端返回的用户对象（不含密码）保持一致
export interface User {
  id: string;
  email: string;
  name: string | null;
  membershipType: MembershipType;
  membershipExpiry: string | null;
}

// 认证上下文类型
interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (
    email: string,
    password: string
  ) => Promise<{ success: boolean; error?: string }>;
  register: (
    email: string,
    password: string,
    name: string
  ) => Promise<{ success: boolean; error?: string }>;
  logout: () => void;
  checkPermissions: (requiredTier: MembershipType) => boolean;
}

// 创建认证上下文
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// 认证提供者组件
export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  // 检查用户会话
  const checkUserSession = async () => {
    try {
      const response = await fetch('/api/auth/me');
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setUser(data.user);
        } else {
          setUser(null);
        }
      } else {
        setUser(null);
      }
    } catch (error) {
      console.error('Failed to fetch user session:', error);
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  // 初始化时检查用户登录状态
  useEffect(() => {
    checkUserSession();
  }, []);

  // 登录函数
  const login = async (email: string, password: string) => {
    try {
      setLoading(true);
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (data.success) {
        setUser(data.user);
        return { success: true };
      } else {
        return { success: false, error: data.error || '登录失败' };
      }
    } catch (error) {
      console.error('登录错误:', error);
      return { success: false, error: '网络错误，请稍后重试' };
    } finally {
      setLoading(false);
    }
  };

  // 注册函数
  const register = async (email: string, password: string, name: string) => {
    try {
      setLoading(true);
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, password, name }),
      });

      const data = await response.json();

      if (data.success) {
        setUser(data.user);
        return { success: true };
      } else {
        return { success: false, error: data.error || '注册失败' };
      }
    } catch (error) {
      console.error('注册错误:', error);
      return { success: false, error: '网络错误，请稍后重试' };
    } finally {
      setLoading(false);
    }
  };

  // 登出函数
  const logout = async () => {
    try {
      await fetch('/api/auth/logout', { method: 'POST' });
    } catch (error) {
      console.error('登出错误:', error);
    } finally {
      setUser(null);
    }
  };

  // 客户端权限检查
  const checkPermissions = (requiredTier: MembershipType): boolean => {
    if (!user) return false;
    if (requiredTier === 'free') return true;
    // 'paid' 意味着任何非免费等级
    return user.membershipType !== 'free';
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        loading,
        login,
        register,
        logout,
        checkPermissions,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

// 自定义 Hook 以使用认证上下文
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// 检查用户是否为会员
export function useIsPremiumUser() {
  const { user } = useAuth();
  return user?.membershipType === 'premium' || user?.membershipType === 'enterprise';
}

// 检查用户是否为企业用户
export function useIsEnterpriseUser() {
  const { user } = useAuth();
  return user?.membershipType === 'enterprise';
}

// 检查会员是否过期
export function useMembershipStatus() {
  const { user } = useAuth();

  if (!user || user.membershipType === 'free') {
    return { isActive: false, isExpired: false, daysLeft: 0 };
  }

  if (!user.membershipExpiry) {
    return { isActive: true, isExpired: false, daysLeft: -1 }; // 永久会员
  }

  const expiryDate = new Date(user.membershipExpiry);
  const now = new Date();
  const isExpired = expiryDate < now;
  const daysLeft = Math.ceil((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

  return {
    isActive: !isExpired,
    isExpired,
    daysLeft: Math.max(0, daysLeft),
  };
}

// 业务唯一键生成规则 - 重构版本
export type UniqueKeyRule = (row: Record<string, any>, context: UniqueKeyContext) => string;

// 新增：唯一键生成上下文
export interface UniqueKeyContext {
  databaseCode: string; // 数据库代码，从路由参数获取
  tableName: string;    // 表名
  importSource?: string; // 导入来源（如文件名）
}

// 导入模式
export type ImportMode = 'insert' | 'upsert' | 'replace';

// Hash算法类型
export type HashAlgorithm = 'md5' | 'sha256' | 'none';

// 表配置接口
export interface TableConfig {
  uniqueKeyRule: UniqueKeyRule;
  importMode: ImportMode;
  description?: string;
  // Hash配置
  hashConfig?: {
    enabled: boolean; // 是否启用Hash模式
    algorithm: HashAlgorithm; // Hash算法
    useHashForIndex: boolean; // 是否使用Hash作为主要索引
  };
  // 可选的字段映射（用于不同数据源的字段名差异）
  fieldMapping?: Record<string, string>;
  // 可选的验证规则
  validationRules?: {
    requiredFields?: string[];
    uniqueFields?: string[];
  };
}

// 扩展的配置对象 - 重构版本
export const tableConfigs: Record<string, TableConfig> = {
  // 医疗器械表配置 - 重构版本
  MedicalDevice: {
    uniqueKeyRule: (row, context) => {
      // 从上下文获取数据源信息，而不是从row.database
      const databaseCode = context.databaseCode;
      
      // 复杂规则：优先使用注册证号，如果没有则使用产品名称+公司名称
      if (row.registrationNumber && row.registrationNumber.trim()) {
        return `${row.registrationNumber}_${databaseCode}`;
      } else {
        return `${row.productName}_${row.companyName}_${databaseCode}`;
      }
    },
    importMode: 'upsert', // 智能同步模式
    description: '医疗器械数据表，支持智能同步更新和Hash优化',
    hashConfig: {
      enabled: true, // 启用Hash模式
      algorithm: 'md5', // 使用MD5算法（性能好，长度短）
      useHashForIndex: true, // 使用Hash作为主要索引
    },
    fieldMapping: {
      // 支持中英文字段名映射
      '产品名称': 'productName',
      '公司名称': 'companyName',
      '注册证号': 'registrationNumber',
      '管理类别': 'managementType',
      '批准日期': 'approvalDate',
      '有效期至': 'validUntil',
      '类别': 'category',
      '结构及用途': 'structureOrUse',
      '生产地址': 'productionAddress',
      '公司地址': 'companyAddress',
      '规格型号': 'specifications',
      '结构组成': 'structure',
      '适用范围': 'scope',
      '储存条件': 'storageConditions',
      '附件': 'accessories',
      '其他内容': 'otherContent',
      '备注': 'notes',
      '分类': 'classification',
      '批准部门': 'approvalDepartment',
      '变更历史': 'changeHistory',
      '是否创新产品': 'isInnovative',
      '是否临床急需': 'isClinicalNeed',
      '是否儿童专用': 'isChildrenSpecific',
      '是否罕见病': 'isRareDisease',
    },
    validationRules: {
      requiredFields: ['productName', 'companyName'], // 移除database必填要求
      uniqueFields: ['businessKey', 'businessKeyHash'],
    },
  },
  
  // 示例：公司表配置 - 重构版本（如果需要）
  Company: {
    uniqueKeyRule: (row, context) => {
      // 公司表使用region字段而不是database字段
      const region = row.region || 'CN';
      
      // 使用公司代码+地区作为唯一键
      if (row.companyCode && row.companyCode.trim()) {
        return `${row.companyCode}_${region}`;
      } else {
        return `${row.companyName}_${region}`;
      }
    },
    importMode: 'upsert', // 智能同步模式
    description: '公司信息表，支持公司代码+地区唯一性',
    hashConfig: {
      enabled: true,
      algorithm: 'md5',
      useHashForIndex: true,
    },
    fieldMapping: {
      // 支持中英文字段名映射
      '公司名称': 'companyName',
      '公司代码': 'companyCode',
      '公司简称': 'companyShortName',
      '地区': 'region',
      '省份': 'province',
      '城市': 'city',
      '地址': 'address',
      '联系电话': 'phone',
      '邮箱': 'email',
      '网站': 'website',
      '成立日期': 'establishDate',
      '注册资本': 'registeredCapital',
      '法人代表': 'legalRepresentative',
      '经营范围': 'businessScope',
      '公司类型': 'companyType',
      '行业分类': 'industryCategory',
      '是否上市': 'isListed',
      '上市代码': 'stockCode',
      '备注': 'notes',
    },
    validationRules: {
      requiredFields: ['companyName', 'region'],
      uniqueFields: ['businessKey', 'businessKeyHash'],
    },
  },

  // 示例：药品表配置 - 重构版本
  Drug: {
    uniqueKeyRule: (row, context) => {
      // 从上下文获取数据源信息
      const databaseCode = context.databaseCode;
      
      // 使用批准文号+数据库类型作为唯一键
      if (row.approvalNumber && row.approvalNumber.trim()) {
        return `${row.approvalNumber}_${databaseCode}`;
      } else {
        return `${row.drugName}_${row.manufacturer}_${databaseCode}`;
      }
    },
    importMode: 'upsert',
    description: '药品信息表，支持批准文号唯一性',
    hashConfig: {
      enabled: true,
      algorithm: 'md5',
      useHashForIndex: true,
    },
    fieldMapping: {
      // 支持中英文字段名映射
      '药品名称': 'drugName',
      '通用名称': 'genericName',
      '商品名称': 'brandName',
      '批准文号': 'approvalNumber',
      '生产厂家': 'manufacturer',
      '剂型': 'dosageForm',
      '规格': 'specification',
      '包装': 'package',
      '适应症': 'indication',
      '用法用量': 'usage',
      '不良反应': 'adverseReaction',
      '禁忌症': 'contraindication',
      '注意事项': 'precautions',
      '批准日期': 'approvalDate',
      '有效期至': 'validUntil',
      '药品分类': 'drugCategory',
      '是否医保': 'isMedicalInsurance',
      '医保类别': 'insuranceCategory',
      '是否基本药物': 'isEssentialDrug',
      '是否OTC': 'isOTC',
      '储存条件': 'storageConditions',
      '备注': 'notes',
    },
    validationRules: {
      requiredFields: ['drugName', 'manufacturer'], // 移除database必填要求
      uniqueFields: ['businessKey', 'businessKeyHash'],
    },
  },

  // PMN数据表配置 - 专用模型示例
  MedicalDevice_US_PMN: {
    uniqueKeyRule: (row, context) => {
      // PMN数据使用K号作为唯一标识
      if (row.kNumber && row.kNumber.trim()) {
        return `${row.kNumber}_us_pmn`;
      } else {
        // 如果没有K号，使用设备名称+申请人
        return `${row.deviceName}_${row.applicant}_us_pmn`;
      }
    },
    importMode: 'upsert',
    description: '美国PMN(510k)数据表，使用K号作为唯一标识',
    hashConfig: {
      enabled: true,
      algorithm: 'md5',
      useHashForIndex: true,
    },
    fieldMapping: {
      // PMN数据的中英文字段映射
      '510(k)编号': 'kNumber',
      'K号': 'kNumber',
      '设备名称': 'deviceName',
      '产品名称': 'deviceName',
      '申请人': 'applicant',
      '公司名称': 'applicant',
      '接收日期': 'dateReceived',
      '决定日期': 'decisionDate',
      '审批决定': 'decision',
      '决定类型': 'decision',
      '法规编号': 'regulationNumber',
      '分类名称': 'classificationName',
      '产品代码': 'productCode',
      '医学专科': 'medicalSpecialty',
      '设备描述': 'deviceDescription',
      '预期用途': 'intendedUse',
      '510(k)摘要': 'summary',
      '器械类别': 'deviceClass',
      '加急审查': 'expeditedReview',
      '第三方审查': 'thirdPartyReview',
      '对比510(k)': 'predicate510k',
      '审查类型': 'reviewType',
    },
         validationRules: {
       requiredFields: ['deviceName', 'applicant'],
       uniqueFields: ['businessKey', 'businessKeyHash'],
     },
  },
  
  // 示例：用户表配置（不使用Hash）
  User: {
    uniqueKeyRule: (row, context) => `${row.email}_${row.organization || 'default'}`,
    importMode: 'upsert',
    description: '用户表，支持邮箱+组织唯一性',
    hashConfig: {
      enabled: false, // 不使用Hash
      algorithm: 'none',
      useHashForIndex: false,
    },
    fieldMapping: {
      '邮箱': 'email',
      '姓名': 'name',
      '组织': 'organization',
      '部门': 'department',
      '职位': 'position',
      '手机号': 'phone',
      '地址': 'address',
      '注册日期': 'registrationDate',
      '最后登录': 'lastLogin',
      '状态': 'status',
    },
    validationRules: {
      requiredFields: ['email', 'name'],
      uniqueFields: ['email'],
    },
  },
};

// 向后兼容的简单配置
export const uniqueKeyConfig: Record<string, UniqueKeyRule> = Object.fromEntries(
  Object.entries(tableConfigs).map(([tableName, config]) => [tableName, config.uniqueKeyRule])
);

// 获取表配置的辅助函数
export function getTableConfig(tableName: string): TableConfig | null {
  return tableConfigs[tableName] || null;
}

// 验证表配置是否存在
export function validateTableConfig(tableName: string): boolean {
  return tableName in tableConfigs;
}

// 获取所有支持的表名
export function getSupportedTables(): string[] {
  return Object.keys(tableConfigs);
}

// 检查表是否启用Hash模式
export function isHashEnabled(tableName: string): boolean {
  const config = getTableConfig(tableName);
  return config?.hashConfig?.enabled === true;
}

// 获取表的Hash算法
export function getHashAlgorithm(tableName: string): HashAlgorithm {
  const config = getTableConfig(tableName);
  return config?.hashConfig?.algorithm || 'none';
}

// 检查表是否使用Hash作为主要索引
export function useHashForIndex(tableName: string): boolean {
  const config = getTableConfig(tableName);
  return config?.hashConfig?.useHashForIndex === true;
}

// 新增：获取表的字段映射
export function getFieldMapping(tableName: string): Record<string, string> | null {
  const config = getTableConfig(tableName);
  return config?.fieldMapping || null;
}

// 新增：获取表的验证规则
export function getValidationRules(tableName: string) {
  const config = getTableConfig(tableName);
  return config?.validationRules || null;
}

// 新增：验证数据是否符合表的验证规则
export function validateData(tableName: string, data: Record<string, any>): { isValid: boolean; errors: string[] } {
  const rules = getValidationRules(tableName);
  const errors: string[] = [];
  
  if (!rules) {
    return { isValid: true, errors: [] };
  }
  
  // 检查必填字段
  if (rules.requiredFields) {
    for (const field of rules.requiredFields) {
      if (!data[field] || data[field].toString().trim() === '') {
        errors.push(`必填字段缺失: ${field}`);
      }
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
} 
import Redis from 'ioredis';
import { db } from './prisma';

// Redis客户端配置
const redis = new Redis(process.env.REDIS_URL || 'redis://localhost:6379');

// 缓存键前缀
const CACHE_PREFIX = 'db_config:';
const FIELD_CONFIG_KEY = (databaseCode: string) => `${CACHE_PREFIX}fields:${databaseCode}`;
const ALL_CONFIG_KEY = (databaseCode: string) => `${CACHE_PREFIX}all:${databaseCode}`;

// 缓存TTL（5分钟）
const CACHE_TTL = 300;

// 配置类型定义
export interface DatabaseFieldConfig {
  fieldName: string;
  displayName: string;
  fieldType: 'text' | 'date' | 'number' | 'boolean' | 'select' | 'json';
  isVisible: boolean;
  isSearchable: boolean;
  isFilterable: boolean;
  isSortable: boolean;
  sortOrder: number;
  listOrder: number;
  detailOrder: number;
  searchType: 'exact' | 'contains' | 'range' | 'date_range' | 'starts_with' | 'ends_with';
  filterType: 'select' | 'input' | 'date_range' | 'checkbox' | 'multi_select' | 'range';
  validationRules?: Record<string, unknown>;
  options?: Record<string, unknown>;
  todetail?: boolean;

  // 统计配置
  isStatisticsEnabled?: boolean;
  statisticsOrder?: number;
  statisticsType?: 'count' | 'sum' | 'avg' | 'min_max' | 'group_by';
  statisticsDisplayName?: string;
  statisticsSortOrder?: 'asc' | 'desc';
  statisticsDefaultLimit?: number;
  statisticsMaxLimit?: number;
  statisticsConfig?: Record<string, unknown>;

  // 导出配置
  isExportable?: boolean;
  exportOrder?: number;
  exportDisplayName?: string;
}

export interface DatabaseSortConfig {
  field: string;
  order: 'asc' | 'desc';
}

export interface DatabaseConfig {
  fields: DatabaseFieldConfig[];
  defaultSort?: DatabaseSortConfig[];
}

// 缓存服务类
export class ConfigCacheService {
  // 获取字段配置
  static async getFieldConfigs(databaseCode: string): Promise<DatabaseFieldConfig[]> {
    const cacheKey = FIELD_CONFIG_KEY(databaseCode);
    
    try {
      // 尝试从缓存获取
      const cached = await redis.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }
    } catch (error) {
      console.warn('Redis缓存读取失败，从数据库获取配置:', error);
    }

    // 从数据库获取
    const fieldConfigs = await db.fieldConfig.findMany({
      where: {
        databaseCode,
        isActive: true,
      },
      orderBy: [
        { listOrder: 'asc' },
        { fieldName: 'asc' },
      ],
      select: {
        fieldName: true,
        displayName: true,
        fieldType: true,
        isVisible: true,
        isSearchable: true,
        isFilterable: true,
        isSortable: true,
        sortOrder: true,
        listOrder: true,
        detailOrder: true,
        searchType: true,
        filterType: true,
        validationRules: true,
        options: true,
        todetail: true,
        // 统计配置字段
        isStatisticsEnabled: true,
        statisticsOrder: true,
        statisticsType: true,
        statisticsDisplayName: true,
        statisticsSortOrder: true,
        statisticsDefaultLimit: true,
        statisticsMaxLimit: true,
        statisticsConfig: true,
        // 导出配置字段
        isExportable: true,
        exportOrder: true,
        exportDisplayName: true,
      },
    });

    const configs = fieldConfigs.map((config: any) => ({
      fieldName: config.fieldName,
      displayName: config.displayName,
      fieldType: config.fieldType,
      isVisible: config.isVisible,
      isSearchable: config.isSearchable,
      isFilterable: config.isFilterable,
      isSortable: config.isSortable,
      sortOrder: config.sortOrder,
      listOrder: config.listOrder,
      detailOrder: config.detailOrder,
      searchType: config.searchType,
      filterType: config.filterType,
      validationRules: config.validationRules as Record<string, unknown> | undefined,
      options: config.options as Record<string, unknown> | undefined,
      todetail: config.todetail || false,

      // 统计配置
      isStatisticsEnabled: config.isStatisticsEnabled || false,
      statisticsOrder: config.statisticsOrder || 0,
      statisticsType: config.statisticsType || 'count',
      statisticsDisplayName: config.statisticsDisplayName,
      statisticsSortOrder: config.statisticsSortOrder || 'desc',
      statisticsDefaultLimit: config.statisticsDefaultLimit || 5,
      statisticsMaxLimit: config.statisticsMaxLimit || 50,
      statisticsConfig: config.statisticsConfig as Record<string, unknown> | undefined,

      // 导出配置
      isExportable: config.isExportable !== false, // 默认为true
      exportOrder: config.exportOrder || 0,
      exportDisplayName: config.exportDisplayName,
    }));

    // 缓存结果
    try {
      await redis.setex(cacheKey, CACHE_TTL, JSON.stringify(configs));
    } catch (error) {
      console.warn('Redis缓存写入失败:', error);
    }

    return configs;
  }



  // 获取完整数据库配置
  static async getDatabaseConfig(databaseCode: string): Promise<DatabaseConfig> {
    const cacheKey = ALL_CONFIG_KEY(databaseCode);
    
    try {
      // 尝试从缓存获取
      const cached = await redis.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }
    } catch (error) {
      console.warn('Redis缓存读取失败，从数据库获取配置:', error);
    }

    // 获取字段配置
    const fields = await this.getFieldConfigs(databaseCode);

    // 获取数据库配置（只获取默认排序，翻页配置使用全局设置）
    const dbConfig = await db.databaseConfig.findUnique({
      where: { code: databaseCode },
      select: {
        defaultSort: true
      }
    });

    // 解析默认排序配置
    let defaultSort: DatabaseSortConfig[] | undefined;
    if (dbConfig?.defaultSort) {
      try {
        defaultSort = dbConfig.defaultSort as DatabaseSortConfig[];
      } catch (error) {
        console.warn(`解析 ${databaseCode} 默认排序配置失败:`, error);
      }
    }

    const config = {
      fields,
      defaultSort,
      // 移除翻页配置，使用全局设置以提升性能
    };

    // 缓存结果
    try {
      await redis.setex(cacheKey, CACHE_TTL, JSON.stringify(config));
    } catch (error) {
      console.warn('Redis缓存写入失败:', error);
    }

    return config;
  }

  // 清除数据库配置缓存
  static async clearDatabaseCache(databaseCode: string): Promise<void> {
    const keys = [
      FIELD_CONFIG_KEY(databaseCode),
      ALL_CONFIG_KEY(databaseCode),
    ];

    try {
      await redis.del(...keys);
    } catch (error) {
      console.warn('Redis缓存清除失败:', error);
    }
  }

  // 清除所有配置缓存
  static async clearAllCache(): Promise<void> {
    try {
      const keys = await redis.keys(`${CACHE_PREFIX}*`);
      if (keys.length > 0) {
        await redis.del(...keys);
      }
    } catch (error) {
      console.warn('Redis缓存清除失败:', error);
    }
  }

  // 预热缓存
  static async warmupCache(databaseCodes: string[]): Promise<void> {
    const promises = databaseCodes.map(code => this.getDatabaseConfig(code));
    await Promise.allSettled(promises);
  }
}



// 配置回退机制 - 硬编码默认配置
export const DEFAULT_CONFIGS: Record<string, DatabaseConfig> = {
  deviceCNImported: {
    fields: [
      {
        fieldName: 'productName',
        displayName: 'Product Name',
        fieldType: 'text',
        isVisible: true,
        isSearchable: true,
        isFilterable: true,
        isSortable: true,
        sortOrder: 1,
        listOrder: 1,
        detailOrder: 1,
        searchType: 'contains',
        filterType: 'input',
      },
      {
        fieldName: 'companyName',
        displayName: 'Company Name',
        fieldType: 'text',
        isVisible: true,
        isSearchable: true,
        isFilterable: true,
        isSortable: true,
        sortOrder: 2,
        listOrder: 2,
        detailOrder: 2,
        searchType: 'contains',
        filterType: 'input',
      },
      {
        fieldName: 'registrationNumber',
        displayName: 'Registration Number',
        fieldType: 'text',
        isVisible: true,
        isSearchable: true,
        isFilterable: true,
        isSortable: true,
        sortOrder: 3,
        listOrder: 3,
        detailOrder: 3,
        searchType: 'contains',
        filterType: 'input',
      },
      {
        fieldName: 'structureOrUse',
        displayName: 'Structure & Use',
        fieldType: 'text',
        isVisible: true,
        isSearchable: true,
        isFilterable: true,
        isSortable: false,
        sortOrder: 4,
        listOrder: 4,
        detailOrder: 4,
        searchType: 'contains',
        filterType: 'input',
      },
      {
        fieldName: 'specifications',
        displayName: 'Specifications',
        fieldType: 'text',
        isVisible: true,
        isSearchable: true,
        isFilterable: true,
        isSortable: false,
        sortOrder: 5,
        listOrder: 5,
        detailOrder: 5,
        searchType: 'contains',
        filterType: 'input',
      },
      {
        fieldName: 'structure',
        displayName: 'Structure',
        fieldType: 'text',
        isVisible: true,
        isSearchable: true,
        isFilterable: true,
        isSortable: false,
        sortOrder: 6,
        listOrder: 6,
        detailOrder: 6,
        searchType: 'contains',
        filterType: 'input',
      },
      {
        fieldName: 'approvalDate',
        displayName: 'Approval Date',
        fieldType: 'date',
        isVisible: true,
        isSearchable: true,
        isFilterable: true,
        isSortable: true,
        sortOrder: 7,
        listOrder: 7,
        detailOrder: 7,
        searchType: 'date_range',
        filterType: 'date_range',
      },
      {
        fieldName: 'validUntil',
        displayName: 'Valid Until',
        fieldType: 'date',
        isVisible: true,
        isSearchable: true,
        isFilterable: true,
        isSortable: true,
        sortOrder: 8,
        listOrder: 8,
        detailOrder: 8,
        searchType: 'date_range',
        filterType: 'date_range',
      },
      {
        fieldName: 'category',
        displayName: 'Category',
        fieldType: 'select',
        isVisible: true,
        isSearchable: true,
        isFilterable: true,
        isSortable: true,
        sortOrder: 9,
        listOrder: 9,
        detailOrder: 9,
        searchType: 'exact',
        filterType: 'select',
      },
      {
        fieldName: 'managementType',
        displayName: 'Management Type',
        fieldType: 'select',
        isVisible: true,
        isSearchable: true,
        isFilterable: true,
        isSortable: true,
        sortOrder: 10,
        listOrder: 10,
        detailOrder: 10,
        searchType: 'exact',
        filterType: 'select',
      },
      {
        fieldName: 'approvalDepartment',
        displayName: 'Approval Department',
        fieldType: 'select',
        isVisible: true,
        isSearchable: true,
        isFilterable: true,
        isSortable: true,
        sortOrder: 11,
        listOrder: 11,
        detailOrder: 11,
        searchType: 'exact',
        filterType: 'select',
      },
      {
        fieldName: 'database',
        displayName: 'Database',
        fieldType: 'text',
        isVisible: true,
        isSearchable: false,
        isFilterable: false,
        isSortable: true,
        sortOrder: 12,
        listOrder: 12,
        detailOrder: 12,
        searchType: 'exact',
        filterType: 'select',
      },
    ],
  },

  // 美国器械分类数据库字段配置
  us_class: {
    fields: [
      {
        fieldName: 'devicename',
        displayName: '器械名称',
        fieldType: 'text',
        isVisible: true,
        isSearchable: true,
        isFilterable: true,
        isSortable: true,
        sortOrder: 1,
        listOrder: 1,
        detailOrder: 1,
        searchType: 'contains',
        filterType: 'input',
      },
      {
        fieldName: 'productcode',
        displayName: '产品代码',
        fieldType: 'text',
        isVisible: true,
        isSearchable: true,
        isFilterable: true,
        isSortable: true,
        sortOrder: 2,
        listOrder: 2,
        detailOrder: 2,
        searchType: 'contains',
        filterType: 'input',
      },
      {
        fieldName: 'deviceclass',
        displayName: '器械类别',
        fieldType: 'select',
        isVisible: true,
        isSearchable: false,
        isFilterable: true,
        isSortable: true,
        sortOrder: 3,
        listOrder: 3,
        detailOrder: 3,
        searchType: 'exact',
        filterType: 'multi_select',
      },
      {
        fieldName: 'medicalspecialty',
        displayName: '医学专科',
        fieldType: 'select',
        isVisible: true,
        isSearchable: false,
        isFilterable: true,
        isSortable: true,
        sortOrder: 4,
        listOrder: 4,
        detailOrder: 4,
        searchType: 'exact',
        filterType: 'multi_select',
      },
      {
        fieldName: 'regulationnumber',
        displayName: '法规编号',
        fieldType: 'text',
        isVisible: true,
        isSearchable: true,
        isFilterable: false,
        isSortable: true,
        sortOrder: 5,
        listOrder: 5,
        detailOrder: 5,
        searchType: 'contains',
        filterType: 'input',
      },
    ],
  },

  // 美国PMN(510k)数据库字段配置 - 专用模型示例
  us_pmn: {
    fields: [
      {
        fieldName: 'kNumber',
        displayName: '510(k)编号',
        fieldType: 'text',
        isVisible: true,
        isSearchable: true,
        isFilterable: false,
        isSortable: true,
        sortOrder: 1,
        listOrder: 1,
        detailOrder: 1,
        searchType: 'contains',
        filterType: 'input',
      },
      {
        fieldName: 'deviceName',
        displayName: '设备名称',
        fieldType: 'text',
        isVisible: true,
        isSearchable: true,
        isFilterable: false,
        isSortable: true,
        sortOrder: 2,
        listOrder: 2,
        detailOrder: 2,
        searchType: 'contains',
        filterType: 'input',
      },
      {
        fieldName: 'applicant',
        displayName: '申请人',
        fieldType: 'text',
        isVisible: true,
        isSearchable: true,
        isFilterable: true,
        isSortable: true,
        sortOrder: 3,
        listOrder: 3,
        detailOrder: 3,
        searchType: 'contains',
        filterType: 'input',
      },
      {
        fieldName: 'decisionDate',
        displayName: '决定日期',
        fieldType: 'date',
        isVisible: true,
        isSearchable: false,
        isFilterable: true,
        isSortable: true,
        sortOrder: 4,
        listOrder: 4,
        detailOrder: 5,
        searchType: 'date_range',
        filterType: 'date_range',
      },
      {
        fieldName: 'decision',
        displayName: '审批决定',
        fieldType: 'select',
        isVisible: true,
        isSearchable: false,
        isFilterable: true,
        isSortable: false,
        sortOrder: 5,
        listOrder: 5,
        detailOrder: 6,
        searchType: 'exact',
        filterType: 'select',
        options: {
          values: [
            'Substantially Equivalent',
            'Not Substantially Equivalent',
            'Pending',
            'Withdrawn'
          ]
        }
      },
      {
        fieldName: 'productCode',
        displayName: '产品代码',
        fieldType: 'text',
        isVisible: true,
        isSearchable: true,
        isFilterable: true,
        isSortable: false,
        sortOrder: 6,
        listOrder: 6,
        detailOrder: 8,
        searchType: 'exact',
        filterType: 'input',
      },
      {
        fieldName: 'medicalSpecialty',
        displayName: '医学专科',
        fieldType: 'select',
        isVisible: true,
        isSearchable: false,
        isFilterable: true,
        isSortable: false,
        sortOrder: 7,
        listOrder: 0, // 列表中不显示
        detailOrder: 9,
        searchType: 'exact',
        filterType: 'select',
      },
      {
        fieldName: 'deviceClass',
        displayName: '器械类别',
        fieldType: 'select',
        isVisible: true,
        isSearchable: false,
        isFilterable: true,
        isSortable: false,
        sortOrder: 8,
        listOrder: 7,
        detailOrder: 7,
        searchType: 'exact',
        filterType: 'select',
        options: {
          values: ['I', 'II', 'III']
        }
      },
      {
        fieldName: 'expeditedReview',
        displayName: '加急审查',
        fieldType: 'boolean',
        isVisible: false, // 详情页才显示
        isSearchable: false,
        isFilterable: true,
        isSortable: false,
        sortOrder: 9,
        listOrder: 0,
        detailOrder: 12,
        searchType: 'exact',
        filterType: 'checkbox',
      },
      {
        fieldName: 'thirdPartyReview',
        displayName: '第三方审查',
        fieldType: 'boolean',
        isVisible: false, // 详情页才显示
        isSearchable: false,
        isFilterable: true,
        isSortable: false,
        sortOrder: 10,
        listOrder: 0,
        detailOrder: 13,
        searchType: 'exact',
        filterType: 'checkbox',
      },
      {
        fieldName: 'deviceDescription',
        displayName: '设备描述',
        fieldType: 'text',
        isVisible: false, // 仅详情页显示
        isSearchable: true,
        isFilterable: false,
        isSortable: false,
        sortOrder: 11,
        listOrder: 0,
        detailOrder: 14,
        searchType: 'contains',
        filterType: 'input',
      },
      {
        fieldName: 'intendedUse',
        displayName: '预期用途',
        fieldType: 'text',
        isVisible: false, // 仅详情页显示
        isSearchable: true,
        isFilterable: false,
        isSortable: false,
        sortOrder: 12,
        listOrder: 0,
        detailOrder: 15,
        searchType: 'contains',
        filterType: 'input',
      }
    ],
  },
};

// 获取数据库配置（带回退机制）
export async function getDatabaseConfig(databaseCode: string): Promise<DatabaseConfig> {
  try {
    // 首先尝试从配置表获取
    const config = await ConfigCacheService.getDatabaseConfig(databaseCode);

    // 如果配置表中有数据，返回配置
    if (config.fields.length > 0) {
      return config;
    }
  } catch (error) {
    console.warn(`获取数据库配置失败 ${databaseCode}:`, error);
  }

  // 回退到硬编码默认配置
  const defaultConfig = DEFAULT_CONFIGS[databaseCode];
  if (defaultConfig) {
    return defaultConfig;
  }

  // 如果连默认配置都没有，返回空配置
  return {
    fields: [],
  };
}

/**
 * 配置验证函数
 */
export function validateFieldConfig(config: DatabaseFieldConfig): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  // 验证必填字段
  if (!config.fieldName) {
    errors.push('fieldName 是必填字段');
  }
  if (!config.displayName) {
    errors.push('displayName 是必填字段');
  }

  // 验证 filterType 和 fieldType 的兼容性
  if (config.filterType === 'multi_select' && config.fieldType === 'boolean') {
    errors.push('布尔字段不应该使用 multi_select 筛选类型');
  }

  if (config.filterType === 'date_range' && config.fieldType !== 'date') {
    errors.push('date_range 筛选类型只能用于日期字段');
  }

  // 验证排序值
  if (config.sortOrder < 0 || config.listOrder < 0 || config.detailOrder < 0) {
    errors.push('排序值不能为负数');
  }

  // 验证筛选类型
  const validFilterTypes = ['select', 'multi_select', 'input', 'date_range', 'checkbox', 'range'];
  if (config.filterType && !validFilterTypes.includes(config.filterType)) {
    errors.push(`无效的筛选类型: ${config.filterType}`);
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 批量验证字段配置
 */
export function validateDatabaseConfig(config: DatabaseConfig): {
  isValid: boolean;
  errors: string[];
  fieldErrors: Record<string, string[]>;
} {
  const errors: string[] = [];
  const fieldErrors: Record<string, string[]> = {};

  // 验证字段配置
  config.fields.forEach((field, index) => {
    const validation = validateFieldConfig(field);
    if (!validation.isValid) {
      fieldErrors[field.fieldName || `field_${index}`] = validation.errors;
    }
  });

  // 检查字段名重复
  const fieldNames = config.fields.map(f => f.fieldName).filter(Boolean);
  const duplicates = fieldNames.filter((name, index) => fieldNames.indexOf(name) !== index);
  if (duplicates.length > 0) {
    errors.push(`重复的字段名: ${duplicates.join(', ')}`);
  }

  return {
    isValid: errors.length === 0 && Object.keys(fieldErrors).length === 0,
    errors,
    fieldErrors
  };
}

// 导出Redis客户端（用于其他模块）
export { redis };
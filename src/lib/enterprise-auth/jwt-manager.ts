import jwt from 'jsonwebtoken';
import { db } from '@/lib/prisma';
import { Redis } from 'ioredis';

// JWT配置
const JWT_CONFIG = {
  ACCESS_TOKEN_SECRET: process.env.JWT_ACCESS_SECRET || 'your-super-secret-access-key',
  REFRESH_TOKEN_SECRET: process.env.JWT_REFRESH_SECRET || 'your-super-secret-refresh-key',
  ACCESS_TOKEN_EXPIRES: '15m',      // 访问令牌15分钟过期
  REFRESH_TOKEN_EXPIRES: '7d',      // 刷新令牌7天过期
  ISSUER: 'medical-device-platform',
  AUDIENCE: 'medical-device-users',
};

// Redis连接（权限缓存）
const redis = new Redis(process.env.REDIS_URL || 'redis://localhost:6379');

// JWT载荷接口
export interface JWTPayload {
  userId: string;
  sessionId: string;
  email: string;
  membershipType: 'FREE' | 'PREMIUM' | 'ENTERPRISE' | 'ADMIN';
  
  // 预计算权限信息（核心优化）
  permissions: {
    databases: Record<string, string[]>;     // 数据库权限映射 {us_class: ['read'], us_pmn: ['read', 'export']}
    features: string[];                      // 功能权限列表 ['advanced_search', 'data_export', 'analytics']
    quotas: {                               // 配额信息
      dailyQueries: number;
      exportLimit: number;
      concurrentSessions: number;
    };
    roles: string[];                        // 用户角色列表
  };
  
  // 会话信息
  sessionInfo: {
    ipAddress?: string;
    userAgent?: string;
    lastActivity: number;
  };
  
  // 标准JWT字段
  iat: number;
  exp: number;
  iss: string;
  aud: string;
}

// 权限缓存键
const CACHE_KEYS = {
  USER_PERMISSIONS: (userId: string) => `perms:user:${userId}`,
  SESSION_BLACKLIST: (sessionId: string) => `blacklist:session:${sessionId}`,
  USER_QUOTA: (userId: string) => `quota:user:${userId}`,
  RATE_LIMIT: (userId: string) => `rate:user:${userId}`,
};

/**
 * 企业级JWT管理器
 * 提供高性能、安全的权限令牌管理
 */
export class EnterpriseJWTManager {
  
  /**
   * 生成访问令牌和刷新令牌
   */
  static async generateTokens(userId: string, sessionInfo: {
    ipAddress?: string;
    userAgent?: string;
  }): Promise<{
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
  }> {
    // 1. 预计算用户权限（核心性能优化）
    const permissions = await this.precomputeUserPermissions(userId);
    
    // 2. 生成唯一会话ID
    const sessionId = this.generateSessionId();
    
    // 3. 获取用户基本信息
    const user = await db.user.findUnique({
      where: { id: userId },
      select: {
        email: true,
        membershipType: true,
        isActive: true,
      },
    });
    
    if (!user || !user.isActive) {
      throw new Error('用户不存在或已被禁用');
    }
    
    // 4. 构建JWT载荷
    const now = Math.floor(Date.now() / 1000);
    const payload: JWTPayload = {
      userId,
      sessionId,
      email: user.email,
      membershipType: user.membershipType as any,
      permissions,
      sessionInfo: {
        ...sessionInfo,
        lastActivity: now,
      },
      iat: now,
      exp: now + (15 * 60), // 15分钟
      iss: JWT_CONFIG.ISSUER,
      aud: JWT_CONFIG.AUDIENCE,
    };
    
    // 5. 生成令牌
    const accessToken = jwt.sign(payload, JWT_CONFIG.ACCESS_TOKEN_SECRET);
    const refreshToken = jwt.sign(
      { userId, sessionId, type: 'refresh' },
      JWT_CONFIG.REFRESH_TOKEN_SECRET,
      { expiresIn: JWT_CONFIG.REFRESH_TOKEN_EXPIRES }
    );
    
    // 6. 存储会话信息到数据库
    await db.userSession.create({
      data: {
        userId,
        sessionId,
        ipAddress: sessionInfo.ipAddress,
        userAgent: sessionInfo.userAgent,
        permissionCache: permissions,
        cacheExpiry: new Date(Date.now() + 15 * 60 * 1000), // 15分钟缓存
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天过期
      },
    });
    
    // 7. 缓存权限信息到Redis
    await this.cacheUserPermissions(userId, permissions);
    
    return {
      accessToken,
      refreshToken,
      expiresIn: 15 * 60, // 15分钟（秒）
    };
  }
  
  /**
   * 验证并解析访问令牌
   */
  static async verifyAccessToken(token: string): Promise<JWTPayload | null> {
    try {
      // 1. 验证JWT签名和有效期
      const payload = jwt.verify(token, JWT_CONFIG.ACCESS_TOKEN_SECRET) as JWTPayload;
      
      // 2. 检查会话是否被拉黑
      const isBlacklisted = await redis.get(CACHE_KEYS.SESSION_BLACKLIST(payload.sessionId));
      if (isBlacklisted) {
        return null;
      }
      
      // 3. 验证会话是否存在且有效
      const session = await db.userSession.findUnique({
        where: { sessionId: payload.sessionId },
        select: {
          isActive: true,
          expiresAt: true,
          userId: true,
        },
      });
      
      if (!session || !session.isActive || session.expiresAt < new Date()) {
        return null;
      }
      
      // 4. 更新最后活动时间
      await this.updateSessionActivity(payload.sessionId);
      
      return payload;
      
    } catch (error) {
      console.error('[JWT] Token验证失败:', error);
      return null;
    }
  }
  
  /**
   * 刷新访问令牌
   */
  static async refreshAccessToken(refreshToken: string): Promise<{
    accessToken: string;
    expiresIn: number;
  } | null> {
    try {
      // 1. 验证刷新令牌
      const refreshPayload = jwt.verify(refreshToken, JWT_CONFIG.REFRESH_TOKEN_SECRET) as any;
      
      if (refreshPayload.type !== 'refresh') {
        return null;
      }
      
      // 2. 检查会话有效性
      const session = await db.userSession.findUnique({
        where: { sessionId: refreshPayload.sessionId },
        include: { user: true },
      });
      
      if (!session || !session.isActive || session.expiresAt < new Date()) {
        return null;
      }
      
      // 3. 重新计算权限（可能有变更）
      const permissions = await this.precomputeUserPermissions(session.userId);
      
      // 4. 生成新的访问令牌
      const now = Math.floor(Date.now() / 1000);
      const newPayload: JWTPayload = {
        userId: session.userId,
        sessionId: session.sessionId,
        email: session.user.email,
        membershipType: session.user.membershipType as any,
        permissions,
        sessionInfo: {
          ipAddress: session.ipAddress || undefined,
          userAgent: session.userAgent || undefined,
          lastActivity: now,
        },
        iat: now,
        exp: now + (15 * 60),
        iss: JWT_CONFIG.ISSUER,
        aud: JWT_CONFIG.AUDIENCE,
      };
      
      const accessToken = jwt.sign(newPayload, JWT_CONFIG.ACCESS_TOKEN_SECRET);
      
      // 5. 更新会话缓存
      await db.userSession.update({
        where: { id: session.id },
        data: {
          permissionCache: permissions,
          cacheExpiry: new Date(Date.now() + 15 * 60 * 1000),
          lastActivity: new Date(),
        },
      });
      
      return {
        accessToken,
        expiresIn: 15 * 60,
      };
      
    } catch (error) {
      console.error('[JWT] 刷新令牌失败:', error);
      return null;
    }
  }
  
  /**
   * 注销会话（拉黑令牌）
   */
  static async revokeSession(sessionId: string): Promise<void> {
    // 1. 拉黑会话
    await redis.setex(
      CACHE_KEYS.SESSION_BLACKLIST(sessionId),
      7 * 24 * 60 * 60, // 7天
      '1'
    );
    
    // 2. 标记会话为非活跃
    await db.userSession.updateMany({
      where: { sessionId },
      data: { isActive: false },
    });
  }
  
  /**
   * 预计算用户权限（核心性能优化）
   */
  private static async precomputeUserPermissions(userId: string): Promise<JWTPayload['permissions']> {
    // 1. 检查缓存
    const cached = await redis.get(CACHE_KEYS.USER_PERMISSIONS(userId));
    if (cached) {
      return JSON.parse(cached);
    }
    
    // 2. 从数据库计算权限
    const user = await db.user.findUnique({
      where: { id: userId },
      include: {
        userRoles: {
          where: { isActive: true },
          include: {
            role: {
              include: {
                rolePermissions: {
                  where: { isActive: true },
                  include: {
                    permission: {
                      where: { isActive: true },
                    },
                  },
                },
              },
            },
          },
        },
        userPermissions: {
          where: { isActive: true },
          include: {
            permission: {
              where: { isActive: true },
            },
          },
        },
      },
    });
    
    if (!user) {
      throw new Error('用户不存在');
    }
    
    // 3. 计算数据库权限
    const databases: Record<string, string[]> = {};
    const features: string[] = [];
    const roles: string[] = [];
    
    // 从角色获取权限
    for (const userRole of user.userRoles) {
      roles.push(userRole.role.name);
      
      for (const rolePermission of userRole.role.rolePermissions) {
        const permission = rolePermission.permission;
        
        if (permission.category === 'database') {
          if (!databases[permission.resource]) {
            databases[permission.resource] = [];
          }
          if (!databases[permission.resource].includes(permission.action)) {
            databases[permission.resource].push(permission.action);
          }
        } else if (permission.category === 'feature') {
          if (!features.includes(permission.name)) {
            features.push(permission.name);
          }
        }
      }
    }
    
    // 从直接权限获取
    for (const userPermission of user.userPermissions) {
      const permission = userPermission.permission;
      
      if (permission.category === 'database') {
        if (!databases[permission.resource]) {
          databases[permission.resource] = [];
        }
        if (!databases[permission.resource].includes(permission.action)) {
          databases[permission.resource].push(permission.action);
        }
      } else if (permission.category === 'feature') {
        if (!features.includes(permission.name)) {
          features.push(permission.name);
        }
      }
    }
    
    // 4. 计算配额（基于会员类型）
    const quotas = this.calculateUserQuotas(user.membershipType as any);
    
    const permissions: JWTPayload['permissions'] = {
      databases,
      features,
      quotas,
      roles,
    };
    
    // 5. 缓存权限（10分钟）
    await this.cacheUserPermissions(userId, permissions, 10 * 60);
    
    return permissions;
  }
  
  /**
   * 计算用户配额
   */
  private static calculateUserQuotas(membershipType: 'FREE' | 'PREMIUM' | 'ENTERPRISE' | 'ADMIN') {
    const quotaConfig = {
      FREE: { dailyQueries: 50, exportLimit: 0, concurrentSessions: 1 },
      PREMIUM: { dailyQueries: 1000, exportLimit: 500, concurrentSessions: 3 },
      ENTERPRISE: { dailyQueries: -1, exportLimit: 5000, concurrentSessions: 10 },
      ADMIN: { dailyQueries: -1, exportLimit: -1, concurrentSessions: -1 },
    };
    
    return quotaConfig[membershipType] || quotaConfig.FREE;
  }
  
  /**
   * 缓存用户权限
   */
  private static async cacheUserPermissions(
    userId: string, 
    permissions: JWTPayload['permissions'], 
    ttl: number = 15 * 60
  ): Promise<void> {
    await redis.setex(
      CACHE_KEYS.USER_PERMISSIONS(userId),
      ttl,
      JSON.stringify(permissions)
    );
  }
  
  /**
   * 更新会话活动时间
   */
  private static async updateSessionActivity(sessionId: string): Promise<void> {
    // 异步更新，不阻塞响应
    setImmediate(async () => {
      try {
        await db.userSession.updateMany({
          where: { sessionId },
          data: { lastActivity: new Date() },
        });
      } catch (error) {
        console.error('[JWT] 更新会话活动时间失败:', error);
      }
    });
  }
  
  /**
   * 生成唯一会话ID
   */
  private static generateSessionId(): string {
    return `sess_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  /**
   * 清理过期缓存和会话
   */
  static async cleanupExpiredSessions(): Promise<void> {
    try {
      // 1. 清理过期的数据库会话
      await db.userSession.deleteMany({
        where: {
          OR: [
            { expiresAt: { lt: new Date() } },
            { lastActivity: { lt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) } }, // 30天未活动
          ],
        },
      });
      
      console.log('[JWT] 已清理过期会话');
    } catch (error) {
      console.error('[JWT] 清理会话失败:', error);
    }
  }
}

/**
 * 快速权限检查器（用于中间件）
 */
export class PermissionChecker {
  
  /**
   * 检查数据库访问权限
   */
  static hasDataBaseAccess(permissions: JWTPayload['permissions'], database: string, action: string = 'read'): boolean {
    return permissions.databases[database]?.includes(action) || false;
  }
  
  /**
   * 检查功能权限
   */
  static hasFeatureAccess(permissions: JWTPayload['permissions'], feature: string): boolean {
    return permissions.features.includes(feature);
  }
  
  /**
   * 检查配额限制
   */
  static checkQuota(permissions: JWTPayload['permissions'], quotaType: keyof JWTPayload['permissions']['quotas'], current: number): boolean {
    const limit = permissions.quotas[quotaType];
    return limit === -1 || current < limit;
  }
  
  /**
   * 检查角色权限
   */
  static hasRole(permissions: JWTPayload['permissions'], role: string): boolean {
    return permissions.roles.includes(role);
  }
}

// 启动定期清理任务
if (typeof window === 'undefined') { // 仅在服务端运行
  setInterval(() => {
    EnterpriseJWTManager.cleanupExpiredSessions();
  }, 60 * 60 * 1000); // 每小时清理一次
} 
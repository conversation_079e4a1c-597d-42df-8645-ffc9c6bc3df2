import { SignJWT, jwtVerify, type JWTPayload } from 'jose';
import { cookies } from 'next/headers';

const secretKey = process.env.SESSION_SECRET;
if (!secretKey) {
  throw new Error('SESSION_SECRET is not set in the environment variables');
}
const encodedKey = new TextEncoder().encode(secretKey);

export async function encrypt(payload: JWTPayload) {
  return new SignJWT(payload)
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime('7d')
    .sign(encodedKey);
}

export async function decrypt(session: string | undefined = '') {
  try {
    if (!session) {
      return null;
    }

    const { payload } = await jwtVerify(session, encodedKey, {
      algorithms: ['HS256'],
    });
    return payload;
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.log('Failed to verify session:', error instanceof Error ? error.message : 'Unknown error');
    }
    return null;
  }
}

export async function createSession(userId: string, userDetails: unknown) {
  const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
  const session = await encrypt({ userId, userDetails, expiresAt, isLoggedIn: true });

  const cookieStore = await cookies();
  cookieStore.set('session', session, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    expires: expiresAt,
    sameSite: 'lax',
    path: '/',
  });
}

export async function getSession() {
  const cookieStore = await cookies();
  const cookie = cookieStore.get('session')?.value;
  const session = await decrypt(cookie);
  return session;
}

export async function updateSession() {
  const session = await getSession();
  if (!session) return;

  const newExpiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
  const newSession = await encrypt({ ...session, expiresAt: newExpiresAt });

  const cookieStore = await cookies();
  const cookie = cookieStore.get('session');
  
  if (cookie) {
    cookieStore.set('session', newSession, {
      ...cookie,
      secure: process.env.NODE_ENV === 'production',
      expires: newExpiresAt,
    });
  }
}

export async function deleteSession() {
  const cookieStore = await cookies();
  cookieStore.delete('session');
} 
import { StaticTableMappingService } from './staticTableMappingService';

/**
 * 应用初始化器
 * 在应用启动时执行必要的初始化操作
 */
export class AppInitializer {
  private static isInitialized = false;
  private static initializationPromise: Promise<void> | null = null;

  /**
   * 初始化应用
   */
  static async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    this.initializationPromise = this._doInitialize();
    return this.initializationPromise;
  }

  private static async _doInitialize(): Promise<void> {
    try {
      console.log('[AppInitializer] 🚀 开始应用初始化...');

      // 初始化静态表映射配置
      await StaticTableMappingService.initialize();

      this.isInitialized = true;
      this.initializationPromise = null;
      
      console.log('[AppInitializer] ✅ 应用初始化完成');

    } catch (error) {
      this.isInitialized = false;
      this.initializationPromise = null;
      console.error('[AppInitializer] ❌ 应用初始化失败:', error);
      throw error;
    }
  }

  /**
   * 获取初始化状态
   */
  static getStatus(): { initialized: boolean } {
    return { initialized: this.isInitialized };
  }

  /**
   * 重新初始化（用于配置刷新）
   */
  static async reinitialize(): Promise<void> {
    console.log('[AppInitializer] 🔄 重新初始化应用...');
    this.isInitialized = false;
    this.initializationPromise = null;
    await this.initialize();
  }
} 
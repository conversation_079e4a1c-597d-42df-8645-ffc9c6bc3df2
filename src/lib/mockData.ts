export interface MockDevice {
  id: number;
  productName: string;
  companyName: string;
  registrationNumber?: string;
  managementType?: string;
  approvalDate?: string;
  validUntil?: string;
  category?: string;
  structureOrUse?: string;
  productionAddress?: string;
  companyAddress?: string;
  specifications?: string;
  structure?: string;
  scope?: string;
  storageConditions?: string;
  accessories?: string;
  otherContent?: string;
  notes?: string;
  classification?: string;
  approvalDepartment?: string;
  changeHistory?: string;
  isInnovative?: boolean;
  isClinicalNeed?: boolean;
  isChildrenSpecific?: boolean;
  isRareDisease?: boolean;
  database: string;
  createdAt?: string;
  updatedAt?: string;
}

export const mockDevices: MockDevice[] = [
  // 中国大陆上市设备
  {
    id: 1,
    productName: '超声喷砂牙周治疗仪',
    companyName: '桂林维润医疗科技有限公司',
    registrationNumber: '桂械注准20232170043',
    managementType: '第二类',
    approvalDate: '2025-06-16',
    validUntil: '2030-06-15',
    category: '国产',
    structureOrUse: '用于牙周病治疗中清除牙石、菌斑等',
    productionAddress: '桂林市七星区东环路东侧GX-G08一号楼3层和4层、二号楼1层',
    companyAddress: '桂林市高新区信息产业园D-07号',
    specifications: 'DQ-40、DQ-80、DQ-81',
    structure: '该产品由超声喷砂牙周治疗仪主机、手柄、工作头、喷砂材料贮藏盒等组成',
    scope: '用于牙周病治疗中清除牙石、菌斑等',
    storageConditions: '常温保存',
    accessories: '手柄、工作头、喷砂材料贮藏盒',
    otherContent: '无',
    notes: '无',
    classification: '6823-4',
    approvalDepartment: '广西壮族自治区药品监督管理局',
    changeHistory: '首次注册',
    isInnovative: false,
    isClinicalNeed: false,
    isChildrenSpecific: false,
    isRareDisease: false,
    database: 'deviceCNImported'
  },
  {
    id: 2,
    productName: '血管造影X射线系统',
    companyName: '西门子医疗器械有限公司',
    registrationNumber: '国械注进20253771234',
    managementType: '第三类',
    approvalDate: '2025-06-15',
    validUntil: '2029-04-07',
    category: '进口',
    structureOrUse: '用于心血管介入诊断和治疗',
    productionAddress: '德国埃尔朗根',
    companyAddress: '上海市浦东新区张江高科技园区',
    specifications: 'Artis icono、Artis pheno',
    structure: '由X射线发生装置、影像增强器、数字化图像处理系统等组成',
    scope: '用于心血管介入诊断和治疗',
    storageConditions: '室温存储',
    accessories: '专用导管、造影剂注射器',
    otherContent: '具备3D成像功能',
    notes: '需专业技术人员操作',
    classification: '6830-1',
    approvalDepartment: '国家药品监督管理局',
    changeHistory: '变更注册',
    isInnovative: true,
    isClinicalNeed: true,
    isChildrenSpecific: false,
    isRareDisease: false,
    database: 'deviceCNImported'
  },
  {
    id: 3,
    productName: '心脏起搏器',
    companyName: '美敦力（上海）医疗器械有限公司',
    registrationNumber: '国械注进20253451122',
    managementType: '第三类',
    approvalDate: '2025-06-14',
    validUntil: '2028-05-12',
    category: '进口',
    structureOrUse: '用于治疗心律失常',
    productionAddress: '美国明尼苏达州',
    companyAddress: '上海市浦东新区世纪大道88号',
    specifications: 'Azure XT、Azure MR',
    structure: '由脉冲发生器、电极导线等组成',
    scope: '用于治疗心律失常，维持正常心率',
    storageConditions: '2-8℃冷藏',
    accessories: '程控器、植入工具',
    otherContent: '具备MRI兼容性',
    notes: '需要定期程控检查',
    classification: '6854-1',
    approvalDepartment: '国家药品监督管理局',
    changeHistory: '首次注册',
    isInnovative: true,
    isClinicalNeed: true,
    isChildrenSpecific: false,
    isRareDisease: false,
    database: 'deviceCNImported'
  },
  // 美国上市设备
  {
    id: 4,
    productName: 'da Vinci Surgical System',
    companyName: 'Intuitive Surgical, Inc.',
    registrationNumber: 'K193414',
    managementType: 'Class II',
    approvalDate: '2025-05-20',
    validUntil: '2030-05-19',
    category: '进口',
    structureOrUse: 'Robot-assisted minimally invasive surgery',
    productionAddress: 'Sunnyvale, California, USA',
    companyAddress: '1020 Kifer Road, Sunnyvale, CA 94086',
    specifications: 'da Vinci Xi, da Vinci SP',
    structure: 'Surgeon console, patient cart, vision cart, EndoWrist instruments',
    scope: 'General laparoscopic surgery, thoracoscopic surgery, urologic surgery',
    storageConditions: 'Room temperature',
    accessories: 'Various EndoWrist instruments, sterile drapes',
    otherContent: '3D HD vision system, motion scaling technology',
    notes: 'Requires specialized training',
    classification: 'BCZ',
    approvalDepartment: 'FDA',
    changeHistory: 'Product enhancement',
    isInnovative: true,
    isClinicalNeed: true,
    isChildrenSpecific: false,
    isRareDisease: false,
    database: 'deviceUS'
  },
  {
    id: 5,
    productName: 'MRI Scanner Pro',
    companyName: 'General Electric Company',
    registrationNumber: 'K201234',
    managementType: 'Class II',
    approvalDate: '2025-04-15',
    validUntil: '2030-04-14',
    category: '进口',
    structureOrUse: 'Magnetic resonance imaging for medical diagnosis',
    productionAddress: 'Milwaukee, Wisconsin, USA',
    companyAddress: '3000 N Grandview Blvd, Waukesha, WI 53188',
    specifications: 'SIGNA Premier, SIGNA Artist',
    structure: 'Superconducting magnet, gradient system, RF system, computer system',
    scope: 'Magnetic resonance imaging of body parts for medical diagnosis',
    storageConditions: 'Controlled environment',
    accessories: 'RF coils, patient table, injection system',
    otherContent: 'AI-powered image reconstruction',
    notes: 'Requires certified technologist',
    classification: 'LNH',
    approvalDepartment: 'FDA',
    changeHistory: 'Software update',
    isInnovative: true,
    isClinicalNeed: false,
    isChildrenSpecific: false,
    isRareDisease: false,
    database: 'deviceUS'
  },
  // 中国器械审评数据
  {
    id: 6,
    productName: '人工智能辅助诊断软件',
    companyName: '北京推想科技有限公司',
    registrationNumber: '正在审评中',
    managementType: '第三类',
    approvalDate: '审评中',
    validUntil: '待定',
    category: '国产',
    structureOrUse: '用于医学影像的AI辅助诊断',
    productionAddress: '北京市海淀区中关村软件园',
    companyAddress: '北京市海淀区中关村大街1号',
    specifications: 'InferRead CT、InferRead DR',
    structure: '由AI算法软件、数据处理模块组成',
    scope: '辅助医生进行CT、DR影像诊断',
    storageConditions: '云端部署',
    accessories: '无',
    otherContent: '基于深度学习技术',
    notes: '正在进行临床试验',
    classification: '6870-1',
    approvalDepartment: '国家药品监督管理局',
    changeHistory: '首次申报',
    isInnovative: true,
    isClinicalNeed: true,
    isChildrenSpecific: false,
    isRareDisease: false,
    database: 'deviceCNEvaluation'
  },
  // 医药专利数据
  {
    id: 7,
    productName: '新型抗肿瘤化合物专利',
    companyName: '中科院药物所',
    registrationNumber: 'CN202310123456.7',
    managementType: '发明专利',
    approvalDate: '2025-03-15',
    validUntil: '2045-03-14',
    category: '国产',
    structureOrUse: '用于治疗实体肿瘤的新型化合物',
    productionAddress: '上海市浦东新区张江高科技园区',
    companyAddress: '上海市浦东新区张江高科技园区',
    specifications: 'CAS号: 123456-78-9',
    structure: '含有吡啶环的有机化合物',
    scope: '适用于肺癌、胃癌等实体肿瘤治疗',
    storageConditions: '避光、干燥保存',
    accessories: '无',
    otherContent: '具有良好的选择性和安全性',
    notes: '正在进行临床前研究',
    classification: 'A61K31/44',
    approvalDepartment: '国家知识产权局',
    changeHistory: '首次申请',
    isInnovative: true,
    isClinicalNeed: true,
    isChildrenSpecific: false,
    isRareDisease: true,
    database: 'freePat'
  }
];

// 模拟搜索功能
export function searchMockDevices(filters: {
  database: string;
  productName?: string;
  companyName?: string;
  registrationNumber?: string;
  structureOrUse?: string;
  category?: string;
  managementType?: string;
  approvalDateFrom?: string;
  approvalDateTo?: string;
  validUntilFrom?: string;
  validUntilTo?: string;
  isInnovative?: boolean;
  isClinicalNeed?: boolean;
  isChildrenSpecific?: boolean;
  isRareDisease?: boolean;
  page?: number;
  limit?: number;
}) {
  let filteredDevices = mockDevices.filter(device => device.database === filters.database);

  // 应用各种筛选条件
  if (filters.productName) {
    filteredDevices = filteredDevices.filter(device =>
      device.productName.toLowerCase().includes(filters.productName?.toLowerCase() || '')
    );
  }

  if (filters.companyName) {
    filteredDevices = filteredDevices.filter(device =>
      device.companyName.toLowerCase().includes(filters.companyName?.toLowerCase() || '')
    );
  }

  if (filters.registrationNumber) {
    filteredDevices = filteredDevices.filter(device =>
      device.registrationNumber?.toLowerCase().includes(filters.registrationNumber?.toLowerCase() || '')
    );
  }

  if (filters.structureOrUse) {
    filteredDevices = filteredDevices.filter(device =>
      device.structureOrUse?.toLowerCase().includes(filters.structureOrUse?.toLowerCase() || '')
    );
  }

  if (filters.category) {
    filteredDevices = filteredDevices.filter(device => device.category === filters.category);
  }

  if (filters.managementType) {
    filteredDevices = filteredDevices.filter(device => device.managementType === filters.managementType);
  }

  if (filters.isInnovative) {
    filteredDevices = filteredDevices.filter(device => device.isInnovative === true);
  }

  if (filters.isClinicalNeed) {
    filteredDevices = filteredDevices.filter(device => device.isClinicalNeed === true);
  }

  if (filters.isChildrenSpecific) {
    filteredDevices = filteredDevices.filter(device => device.isChildrenSpecific === true);
  }

  if (filters.isRareDisease) {
    filteredDevices = filteredDevices.filter(device => device.isRareDisease === true);
  }

  // 日期筛选
  if (filters.approvalDateFrom) {
    const fromDate = filters.approvalDateFrom;
    filteredDevices = filteredDevices.filter(device => {
      if (!device.approvalDate || device.approvalDate === '审评中') return false;
      return device.approvalDate >= fromDate;
    });
  }

  if (filters.approvalDateTo) {
    const toDate = filters.approvalDateTo;
    filteredDevices = filteredDevices.filter(device => {
      if (!device.approvalDate || device.approvalDate === '审评中') return false;
      return device.approvalDate <= toDate;
    });
  }

  if (filters.validUntilFrom) {
    const fromDate = filters.validUntilFrom;
    filteredDevices = filteredDevices.filter(device => {
      if (!device.validUntil || device.validUntil === '待定') return false;
      return device.validUntil >= fromDate;
    });
  }

  if (filters.validUntilTo) {
    const toDate = filters.validUntilTo;
    filteredDevices = filteredDevices.filter(device => {
      if (!device.validUntil || device.validUntil === '待定') return false;
      return device.validUntil <= toDate;
    });
  }

  // 分页
  const page = filters.page || 1;
  const limit = filters.limit || 20;
  const offset = (page - 1) * limit;
  const totalCount = filteredDevices.length;
  const totalPages = Math.ceil(totalCount / limit);
  const paginatedDevices = filteredDevices.slice(offset, offset + limit);

  return {
    devices: paginatedDevices,
    pagination: {
      page,
      limit,
      totalCount,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    }
  };
}

// 根据ID获取设备详情
export function getMockDeviceById(id: number): MockDevice | null {
  return mockDevices.find(device => device.id === id) || null;
}

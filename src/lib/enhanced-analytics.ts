// 增强的Analytics事件追踪系统

export interface AnalyticsEvent {
  event: string;
  data?: Record<string, any>;
  timestamp?: number;
  sessionId?: string;
  userId?: string;
}

export interface UserBehaviorEvent extends AnalyticsEvent {
  event: 
    | 'page_view'
    | 'database_search'
    | 'advanced_search'
    | 'filter_applied'
    | 'filter_cleared'
    | 'sort_applied'
    | 'data_export'
    | 'pagination_change'
    | 'detail_view'
    | 'user_login'
    | 'user_logout'
    | 'search_suggestion_clicked'
    | 'table_column_resized'
    | 'table_scrolled'
    | 'stats_panel_toggled'
    | 'error_occurred'
    | 'performance_metric';
}

export interface PerformanceEvent extends AnalyticsEvent {
  event: 'performance_metric';
  data: {
    metric: 'page_load_time' | 'api_response_time' | 'search_time' | 'export_time';
    value: number;
    url?: string;
    database?: string;
  };
}

export interface ErrorEvent extends AnalyticsEvent {
  event: 'error_occurred';
  data: {
    error_type: 'api_error' | 'client_error' | 'network_error';
    error_message: string;
    error_stack?: string;
    url?: string;
    user_agent?: string;
  };
}

class EnhancedAnalytics {
  private sessionId: string;
  private userId: string | null = null;
  private queue: AnalyticsEvent[] = [];
  private isOnline = true;
  private batchSize = 10;
  private maxQueueSize = 100; // 最大队列大小
  private flushInterval = 5000; // 5秒
  private performanceObserver?: PerformanceObserver;

  constructor() {
    this.sessionId = this.getOrCreateSessionId();
    this.initializePerformanceTracking();
    this.initializeErrorTracking();
    this.initializeNetworkStatusTracking();
    this.startBatchFlush();
  }

  // 获取或创建会话ID
  private getOrCreateSessionId(): string {
    if (typeof window === 'undefined') return '';
    
    let sessionId = sessionStorage.getItem('analytics_session_id');
    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
      sessionStorage.setItem('analytics_session_id', sessionId);
    }
    return sessionId;
  }

  // 设置用户ID
  setUserId(userId: string | null) {
    this.userId = userId;
  }

  // 追踪事件
  track(event: UserBehaviorEvent) {
    const enhancedEvent: AnalyticsEvent = {
      ...event,
      timestamp: event.timestamp || Date.now(),
      sessionId: this.sessionId,
      userId: this.userId,
      data: {
        ...event.data,
        url: typeof window !== 'undefined' ? window.location.href : undefined,
        referrer: typeof document !== 'undefined' ? document.referrer : undefined,
        user_agent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined,
        viewport: typeof window !== 'undefined' ? {
          width: window.innerWidth,
          height: window.innerHeight
        } : undefined,
        screen: typeof screen !== 'undefined' ? {
          width: screen.width,
          height: screen.height
        } : undefined,
      }
    };

    this.addToQueue(enhancedEvent);
  }

  // 追踪性能指标
  trackPerformance(metric: PerformanceEvent['data']) {
    this.track({
      event: 'performance_metric',
      data: metric
    });
  }

  // 追踪错误
  trackError(error: ErrorEvent['data']) {
    this.track({
      event: 'error_occurred',
      data: error
    });
  }

  // 追踪页面浏览
  trackPageView(additionalData?: Record<string, any>) {
    this.track({
      event: 'page_view',
      data: {
        ...additionalData,
        page_title: typeof document !== 'undefined' ? document.title : undefined,
        page_load_time: this.getPageLoadTime(),
      }
    });
  }

  // 追踪搜索行为
  trackSearch(searchData: {
    query: string;
    database: string;
    search_type: 'simple' | 'advanced';
    results_count?: number;
    search_time?: number;
    filters_applied?: Record<string, any>;
  }) {
    this.track({
      event: searchData.search_type === 'advanced' ? 'advanced_search' : 'database_search',
      data: searchData
    });
  }

  // 追踪用户交互
  trackInteraction(interaction: {
    type: 'click' | 'scroll' | 'resize' | 'hover';
    element: string;
    value?: any;
    database?: string;
  }) {
    this.track({
      event: 'user_interaction',
      data: interaction
    });
  }

  // 添加到队列
  private addToQueue(event: AnalyticsEvent) {
    // 防止队列过大导致内存泄漏
    if (this.queue.length >= this.maxQueueSize) {
      console.warn('Analytics queue is full, dropping oldest events');
      this.queue = this.queue.slice(-this.maxQueueSize / 2); // 保留一半
    }

    this.queue.push(event);

    // 如果队列满了或者是重要事件，立即发送
    if (this.queue.length >= this.batchSize || this.isImportantEvent(event)) {
      this.flush();
    }
  }

  // 判断是否为重要事件
  private isImportantEvent(event: AnalyticsEvent): boolean {
    const importantEvents = ['error_occurred', 'user_login', 'user_logout'];
    return importantEvents.includes(event.event);
  }

  // 批量发送事件
  private async flush() {
    if (this.queue.length === 0 || !this.isOnline) return;

    const events = [...this.queue];
    this.queue = [];

    try {
      const response = await fetch('/api/analytics/batch-track', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ events }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.warn('Analytics batch send failed:', response.status, errorText);

        // 对于某些错误，不重新加入队列（避免无限重试）
        if (response.status !== 400 && response.status !== 422) {
          // 只对服务器错误重试，不对客户端错误重试
          this.queue.unshift(...events);
        }
      }
    } catch (error) {
      // 网络错误，重新加入队列
      this.queue.unshift(...events);
      console.warn('Analytics batch send failed:', error);
    }
  }

  // 定期刷新队列
  private startBatchFlush() {
    if (typeof window === 'undefined') return;
    
    setInterval(() => {
      this.flush();
    }, this.flushInterval);

    // 页面卸载时发送剩余事件
    window.addEventListener('beforeunload', () => {
      if (this.queue.length > 0) {
        // 使用 sendBeacon 确保数据发送
        navigator.sendBeacon('/api/analytics/batch-track', JSON.stringify({
          events: this.queue
        }));
      }
    });
  }

  // 初始化性能追踪
  private initializePerformanceTracking() {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) return;

    // 追踪页面加载性能
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        if (navigation) {
          this.trackPerformance({
            metric: 'page_load_time',
            value: navigation.loadEventEnd - navigation.fetchStart,
            url: window.location.href
          });
        }
      }, 0);
    });

    // 追踪资源加载性能
    this.performanceObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'measure') {
          this.trackPerformance({
            metric: 'api_response_time',
            value: entry.duration,
            url: entry.name
          });
        }
      }
    });

    this.performanceObserver.observe({ entryTypes: ['measure'] });
  }

  // 初始化错误追踪
  private initializeErrorTracking() {
    if (typeof window === 'undefined') return;

    // 捕获JavaScript错误
    window.addEventListener('error', (event) => {
      this.trackError({
        error_type: 'client_error',
        error_message: event.message,
        error_stack: event.error?.stack,
        url: event.filename,
      });
    });

    // 捕获Promise rejection
    window.addEventListener('unhandledrejection', (event) => {
      this.trackError({
        error_type: 'client_error',
        error_message: event.reason?.message || 'Unhandled Promise Rejection',
        error_stack: event.reason?.stack,
      });
    });
  }

  // 初始化网络状态追踪
  private initializeNetworkStatusTracking() {
    if (typeof window === 'undefined' || !('navigator' in window)) return;

    this.isOnline = navigator.onLine;

    window.addEventListener('online', () => {
      this.isOnline = true;
      this.flush(); // 网络恢复时发送队列中的事件
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
    });
  }

  // 获取页面加载时间
  private getPageLoadTime(): number | undefined {
    if (typeof performance === 'undefined') return undefined;
    
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    return navigation ? navigation.loadEventEnd - navigation.fetchStart : undefined;
  }

  // 手动刷新队列
  forceFlush() {
    this.flush();
  }

  // 获取队列状态
  getQueueStatus() {
    return {
      queueLength: this.queue.length,
      isOnline: this.isOnline,
      sessionId: this.sessionId,
      userId: this.userId
    };
  }
}

// 单例实例
export const analytics = new EnhancedAnalytics();

// 便捷函数
export const trackEvent = (event: UserBehaviorEvent) => analytics.track(event);
export const trackPageView = (data?: Record<string, any>) => analytics.trackPageView(data);
export const trackSearch = (data: Parameters<typeof analytics.trackSearch>[0]) => analytics.trackSearch(data);
export const trackError = (data: ErrorEvent['data']) => analytics.trackError(data);
export const trackPerformance = (data: PerformanceEvent['data']) => analytics.trackPerformance(data);
export const setUserId = (userId: string | null) => analytics.setUserId(userId);

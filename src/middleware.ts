import { type NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { updateSession } from '@/lib/session';

// 配置中间件适用的路由
export const config = {
  matcher: [
    /*
     * 匹配除了以下路径之外的所有请求路径:
     * - _next/static (静态文件)
     * - _next/image (图片优化文件)
     * - favicon.ico (网站图标)
     * - /api/auth/ (认证相关接口，避免重复记录)
     */
    '/((?!_next/static|_next/image|favicon.ico|api/auth/).*)',
  ],
};

const RATE_LIMIT_WINDOW = 60 * 1000; // 1分钟
const RATE_LIMIT_COUNT = 30; // 1分钟内最多30次请求

function getIP(request: NextRequest) {
  const headers = request.headers;
  // 在 Edge 环境中，x-forwarded-for 是获取真实 IP 的标准方式。
  // request.ip 在非 Node.js 环境中可能不存在。
  const ip = headers.get('x-forwarded-for')?.split(',')[0].trim() || headers.get('x-real-ip')?.trim();
  return ip || '127.0.0.1'; // 如果都获取不到，则使用本地地址作为备用
}

export async function middleware(request: NextRequest) {
  // 在开发环境中，可以暂时跳过所有中间件逻辑
  if (process.env.NODE_ENV === 'development') {
    return NextResponse.next();
  }

  const ip = getIP(request);

  try {
    // 1. 检查IP是否被屏蔽
    const blockedIp = await db.blockedIp.findFirst({
      where: {
        ip: ip,
        expiresAt: {
          gt: new Date(),
        },
      },
    });

    if (blockedIp) {
      console.log(`[Middleware] Blocked IP access: ${ip}`);
      return new NextResponse('Too many requests', { status: 429 });
    }

    // 并行处理日志记录和速率限制
    await Promise.all([
      logActivity(request, ip),
      checkRateLimit(ip)
    ]);

  } catch (error) {
    console.error('[Middleware] Error:', error);
    // 即使中间件出错，也应保证网站核心功能可访问
  }

  // 尝试更新会话，不阻塞主流程
  await updateSession();

  // 正常处理请求
  return NextResponse.next();
}

async function checkRateLimit(ip: string) {
  const windowStart = new Date(Date.now() - RATE_LIMIT_WINDOW);
  
  const requestCount = await db.activityLog.count({
    where: {
      ip: ip,
      createdAt: {
        gt: windowStart,
      },
    },
  });

  if (requestCount > RATE_LIMIT_COUNT) {
    console.warn(`[Middleware] Rate limit exceeded for IP: ${ip}. Blocking now.`);
    const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 屏蔽5分钟
    
    await db.blockedIp.upsert({
      where: { ip: ip },
      update: { expiresAt },
      create: { ip, reason: 'Rate limit exceeded', expiresAt },
    });
  }
}

async function logActivity(request: NextRequest, ip: string) {
  const { pathname, search } = request.nextUrl;
  // 采集referer、database、eventType、sessionId等字段
  const referer = request.headers.get('referer') || undefined;
  // database参数可从路径或查询参数中提取，示例仅做简单提取
  const url = new URL(request.url);
  const database = url.searchParams.get('database') || undefined;
  // eventType和sessionId可通过header或cookie传递，示例做兼容性采集
  const eventType = request.headers.get('x-event-type') || undefined;
  const sessionId = request.cookies.get('sessionId')?.value || undefined;

  await db.activityLog.create({
    data: {
      ip,
      userAgent: request.headers.get('user-agent'),
      path: pathname,
      method: request.method,
      queryParams: search ? search.slice(1) : undefined,
      referer,
      database,
      eventType,
      sessionId,
    }
  });
} 
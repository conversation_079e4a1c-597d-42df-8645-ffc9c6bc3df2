import { describe, it, expect, vi, afterEach, beforeEach } from 'vitest';
import { middleware } from './middleware';
import { NextRequest } from 'next/server';
import type { NewActivityLog, BlockedIp, NewBlockedIp } from '@/db/schema';

// 模拟数据库模块
// 这是最终的模拟方案，确保所有链式调用都存在
vi.mock('@/db', () => {
    const mockDb = {
        select: vi.fn().mockReturnThis(),
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockResolvedValue([]), // 默认返回空数组
        limit: vi.fn().mockReturnThis(),
        insert: vi.fn().mockReturnThis(),
        values: vi.fn().mockResolvedValue([]),
        onConflictDoUpdate: vi.fn().mockReturnThis(),
    };
    // 让 insert().values() 可以工作
    (mockDb.insert as unknown).mockImplementation(() => ({
        values: (mockDb.values as unknown),
        onConflictDoUpdate: (mockDb.onConflictDoUpdate as unknown)
    }));
    // 让 select().from().where() 可以工作
    (mockDb.select as unknown).mockImplementation(() => ({
        from: vi.fn().mockReturnThis(),
        where: mockDb.where,
        limit: mockDb.limit,
    }));
    return { db: mockDb };
});

describe('middleware', () => {
    let db: unknown;

    beforeEach(async () => {
        // 在每个测试前，重新导入最新的模拟db对象并重置
        db = (await import('@/db')).db;
        vi.clearAllMocks();
    });

    it('should log user activity', async () => {
        const request = new NextRequest('http://localhost:3000/some-path?query=123', {
            headers: { 'x-forwarded-for': '***************' },
        });
        await middleware(request);
        expect(db.insert).toHaveBeenCalledOnce();
        expect(db.values).toHaveBeenCalledWith(expect.objectContaining({
            ip: '***************',
            path: '/some-path',
        }));
    });

    it('should block a blocked IP address', async () => {
        const blockedIp: BlockedIp = { id: 1, ip: '***********', reason: 'Test', createdAt: new Date(), expiresAt: new Date(Date.now() + 360000) };
        db.where.mockResolvedValue([blockedIp]); // 配置 where 的返回值
        const request = new NextRequest('http://localhost:3000/', {
            headers: { 'x-forwarded-for': '***********' },
        });
        const response = await middleware(request);
        expect(response.status).toBe(429);
    });

    it('should trigger rate limit and block IP', async () => {
        const userIp = '********';
        // 第一次 select (检查屏蔽) 返回空
        db.where.mockResolvedValueOnce([]);
        // 第二次 select (检查速率) 返回超限计数
        // 注意：这里需要模拟两次 where 调用，因为 middleware 中有两个 select
        db.where.mockResolvedValueOnce([]); // for the initial block check
        db.where.mockResolvedValueOnce([{ count: 50 }]); // for the rate limit check
        
        // 我们需要更精确地模拟
        vi.spyOn(db, 'where')
          .mockResolvedValueOnce([]) // First call for blocked IP check
          .mockResolvedValueOnce([{count: 50}]); // Second call for rate limit check

        const request = new NextRequest('http://localhost:3000/page', {
            headers: { 'x-forwarded-for': userIp },
        });
        await middleware(request);
        // 应该有两次 insert: 一次是日志，一次是屏蔽IP
        expect(db.insert).toHaveBeenCalledTimes(2);
        // 验证第二次 insert 调用是为了屏蔽IP
        expect(db.values).toHaveBeenCalledWith(expect.objectContaining({
            ip: userIp,
            reason: 'Rate limit exceeded',
        }));
    });
}); 
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id               String         @id @default(uuid())
  email            String         @unique @db.VarChar(255)
  password         String
  name             String         @db.VarChar(100)
  membershipType   String         @default("free") @db.VarChar(20)
  membershipExpiry DateTime?
  isActive         Boolean        @default(true)
  emailVerified    Boolean        @default(false)
  createdAt        DateTime       @default(now())
  updatedAt        DateTime       @updatedAt
  activityLogs     ActivityLog[]
  sessions         UserSession[]
  searchAnalytics  SearchAnalytics[]
}

model DatabaseConfig {
  id          String   @id @default(uuid())
  code        String   @unique @db.VarChar(50)
  name        String   @db.VarChar(100)
  category    String   @db.VarChar(50)
  description String?
  accessLevel String   @default("free") @db.VarChar(20)
  
  // 新增：动态表名配置字段
  tableName   String?  @db.VarChar(100)  // PostgreSQL实际表名
  modelName   String?  @db.VarChar(100)  // Prisma模型名

  // 默认排序配置 [{"field": "productcode", "order": "desc"}, {"field": "id", "order": "asc"}]
  defaultSort Json?

  // 导出配置
  maxExportLimit     Int  @default(10000)
  defaultExportLimit Int  @default(1000)
  exportConfig       Json?

  isActive    Boolean  @default(true)
  sortOrder   Int      @default(autoincrement())
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model UserSession {
  id        String   @id @default(uuid())
  userId    String
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}



model Company {
  id                 String   @id @default(uuid())
  companyName        String
  companyCode        String?  @db.VarChar(50)
  companyShortName   String?
  region             String   @db.VarChar(50)
  province           String?
  city               String?
  address            String?
  phone              String?
  email              String?
  website            String?
  establishDate      DateTime?
  registeredCapital  String?
  legalRepresentative String?
  businessScope      String?
  companyType        String?
  industryCategory   String?
  isListed           Boolean? @default(false)
  stockCode          String?
  notes              String?
  database           String   @db.VarChar(50)
  businessKey        String   @unique @db.VarChar(200)
  businessKeyHash    String?  @unique
  dataVersion        Int      @default(1)
  isActive           Boolean  @default(true)
  importedAt         DateTime @default(now())
  updatedAt          DateTime @updatedAt
  createdAt          DateTime @default(now())

  @@index([database])
  @@index([businessKey])
  @@index([businessKeyHash])
}

model ActivityLog {
  id          String   @id @default(uuid())
  userId      String?
  ip          String   @db.VarChar(50)
  userAgent   String?  @db.VarChar(500)
  path        String   @db.VarChar(500)
  method      String   @db.VarChar(20)
  queryParams String?  @db.Text
  referer     String?  @db.VarChar(500)
  database    String?  @db.VarChar(50)
  eventType   String?  @db.VarChar(50)
  sessionId   String?  @db.VarChar(100)
  createdAt   DateTime @default(now())
  user        User?    @relation(fields: [userId], references: [id])

  // 性能优化索引
  @@index([createdAt])
  @@index([eventType])
  @@index([database])
  @@index([sessionId])
  @@index([ip])
  @@index([userId])
  @@index([createdAt, eventType])
  @@index([createdAt, database])
  @@index([sessionId, eventType])
}

model SearchAnalytics {
  id            String   @id @default(uuid())
  userId        String?
  sessionId     String?  @db.VarChar(100)
  database      String   @db.VarChar(50)
  searchType    String   @db.VarChar(20) // 'simple', 'advanced', 'filter'
  searchQuery   String?  @db.Text        // 主要搜索词
  searchFields  Json?                    // 搜索的字段 {"productName": "迈瑞", "companyName": ""}
  filters       Json?                    // 应用的筛选条件
  sortBy        String?  @db.VarChar(50) // 排序字段
  sortOrder     String?  @db.VarChar(10) // 'asc', 'desc'
  resultsCount  Int?                     // 搜索结果数量
  searchTime    Int?                     // 搜索耗时(毫秒)
  ip            String   @db.VarChar(50)
  userAgent     String?  @db.VarChar(500)
  createdAt     DateTime @default(now())
  user          User?    @relation(fields: [userId], references: [id])

  // 索引优化
  @@index([createdAt])
  @@index([database])
  @@index([searchType])
  @@index([userId])
  @@index([sessionId])
  @@index([database, createdAt])
  @@index([searchType, database])
}

model BlockedIp {
  id        String   @id @default(uuid())
  ip        String   @unique @db.VarChar(50)
  reason    String?
  expiresAt DateTime
  createdAt DateTime @default(now())
}

model DataChangeLog {
  id              String   @id @default(uuid())
  businessKey     String   @db.VarChar(200)
  businessKeyHash String?
  operation       String   @db.VarChar(20)
  oldData         Json?
  newData         Json?
  changeReason    String?  @db.VarChar(500)
  importedBy      String?  @db.VarChar(100)
  importedFrom    String?  @db.VarChar(200)
  createdAt       DateTime @default(now())
  
  @@index([businessKey])
  @@index([businessKeyHash])
  @@index([operation])
  @@index([createdAt])
}

// 字段类型枚举
enum FieldType {
  text
  date
  number
  boolean
  select
  json
}

// 搜索类型枚举
enum SearchType {
  exact
  contains
  range
  date_range
  starts_with
  ends_with
}

// 筛选类型枚举
enum FilterType {
  select
  input
  date_range
  checkbox
  multi_select
  range
}

enum StatisticsType {
  count      // 计数统计
  sum        // 求和统计
  avg        // 平均值统计
  min_max    // 最值统计
  group_by   // 分组统计
}

// 字段配置表
model FieldConfig {
  id             String     @id @default(uuid())
  databaseCode   String     @db.VarChar(50)
  fieldName      String     @db.VarChar(100)
  displayName    String     @db.VarChar(100)
  fieldType      FieldType  @default(text)
  isVisible      Boolean    @default(true)
  isSearchable   Boolean    @default(false)
  isFilterable   Boolean    @default(false)
  isSortable     Boolean    @default(false)
  sortOrder      Int        @default(0)
  listOrder      Int        @default(0)
  detailOrder    Int        @default(0)
  searchType     SearchType @default(contains)
  filterType     FilterType @default(select)
  validationRules Json?
  options        Json?
  todetail       Boolean    @default(false)

  // 统计配置字段
  isStatisticsEnabled     Boolean    @default(false)
  statisticsOrder         Int        @default(0)
  statisticsType          StatisticsType @default(count)
  statisticsDisplayName   String?    @db.VarChar(100)
  statisticsConfig        Json?
  statisticsSortOrder     String?    @default("desc") @db.VarChar(10)
  statisticsDefaultLimit  Int        @default(5)
  statisticsMaxLimit      Int        @default(50)

  // 导出配置字段
  isExportable            Boolean    @default(true)
  exportOrder             Int        @default(0)
  exportDisplayName       String?    @db.VarChar(100)

  // 新增：筛选器显示顺序字段
  filterOrder             Int        @default(0)

  isActive       Boolean    @default(true)
  createdAt      DateTime   @default(now())
  updatedAt      DateTime   @updatedAt

  @@unique([databaseCode, fieldName])
  @@index([databaseCode])
  @@index([isActive])
  @@index([isStatisticsEnabled])
  @@index([filterOrder])
}



// US Premarket Notification (510k) 数据模型
model USPremarketNotification {
  id                String   @id @default(uuid())
  knumber           String?  @db.VarChar(20)    // 510(k) Number
  applicant         String?                     // Applicant
  contact           String?                     // Contact
  street1           String?                     // Street Address 1
  street2           String?                     // Street Address 2
  city              String?  @db.VarChar(100)   // City
  state             String?  @db.VarChar(50)    // State
  country_code      String?  @db.VarChar(10)    // Country Code
  zip               String?  @db.VarChar(20)    // ZIP Code
  postal_code       String?  @db.VarChar(20)    // Postal Code
  datereceived      DateTime?                   // Date Received
  decisiondate      DateTime?                   // Decision Date
  decision          String?  @db.VarChar(100)   // Decision
  reviewadvisecomm  String?                     // Review Advisory Committee
  productcode       String?  @db.VarChar(20)    // Product Code
  stateorsumm       String?                     // Statement or Summary
  classadvisecomm   String?                     // Classification Advisory Committee
  sspindicator      String?  @db.VarChar(10)    // SSP Indicator
  type              String?  @db.VarChar(50)    // Type
  thirdparty        String?  @db.VarChar(10)    // Third Party
  expeditedreview   String?  @db.VarChar(10)    // Expedited Review
  devicename        String?                     // Device Name
  source_file       String?                     // Source File
  source_time       String?                     // Source Time
  decision_year     String?                        // Decision Year

  @@map("us_pmn")
  @@index([knumber])
  @@index([applicant])
  @@index([decisiondate])
  @@index([decision])
  @@index([productcode])
  @@index([devicename])
  @@index([decision_year])
}

// 联系表单提交记录
model ContactSubmission {
  id        String   @id @default(uuid())
  name      String   @db.VarChar(100)
  email     String   @db.VarChar(255)
  subject   String   @db.VarChar(200)
  category  String   @db.VarChar(50)
  message   String   @db.Text
  ip        String?  @db.VarChar(45)
  userAgent String?  @db.VarChar(500)
  status    String   @default("pending") @db.VarChar(20) // pending, read, replied, closed
  adminNotes String? @db.Text
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([email])
  @@index([category])
  @@index([status])
  @@index([createdAt])
}

// 美国器械分类目录模型
model USClass {
  id                          String  @id @default(uuid())
  review_panel               String?
  medicalspecialty           String?
  productcode                String?
  devicename                 String?
  deviceclass                String?
  unclassified_reason        String?
  gmpexemptflag              String?
  thirdpartyflag             String?
  reviewcode                 String?
  regulationnumber           String?
  submission_type_id         String?
  definition                 String?
  physicalstate              String?
  technicalmethod            String?
  targetarea                 String?
  implant_flag               String?
  life_sustain_support_flag  String?
  summarymalfunctionreporting String?
  source_file                String?
  source_time                String?
  
  @@map("us_class")
  @@index([productcode])
  @@index([devicename])
  @@index([deviceclass])
  @@index([medicalspecialty])
  @@index([regulationnumber])
}



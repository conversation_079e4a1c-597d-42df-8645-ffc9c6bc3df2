<!DOCTYPE html>
<html>
<head>
    <title>Test Statistics API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .stats-container { margin: 20px 0; }
        .stat-item { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>US Class Statistics Test</h1>
    
    <h2>Old API (硬编码)</h2>
    <div id="old-stats" class="stats-container">Loading...</div>
    
    <h2>New API (可配置)</h2>
    <div id="new-stats" class="stats-container">Loading...</div>

    <script>
        // 测试旧API
        fetch('/api/stats/us_class')
            .then(response => response.json())
            .then(data => {
                const container = document.getElementById('old-stats');
                if (data.success) {
                    container.innerHTML = `
                        <div class="success">✅ 旧API工作正常</div>
                        <div class="stat-item">
                            <strong>基础统计:</strong><br>
                            总数: ${data.data.basic.total}<br>
                            活跃: ${data.data.basic.active}<br>
                            非活跃: ${data.data.basic.inactive}
                        </div>
                        <div class="stat-item">
                            <strong>类别统计:</strong> ${data.data.categories ? data.data.categories.length : 0} 个类别
                        </div>
                        <div class="stat-item">
                            <strong>企业统计:</strong> ${data.data.companies ? data.data.companies.length : 0} 个企业
                        </div>
                    `;
                } else {
                    container.innerHTML = `<div class="error">❌ 旧API失败: ${data.error}</div>`;
                }
            })
            .catch(error => {
                document.getElementById('old-stats').innerHTML = `<div class="error">❌ 旧API错误: ${error.message}</div>`;
            });

        // 测试新API
        fetch('/api/stats/us_class/configurable')
            .then(response => response.json())
            .then(data => {
                const container = document.getElementById('new-stats');
                if (data.success) {
                    container.innerHTML = `
                        <div class="success">✅ 新API工作正常</div>
                        <div class="stat-item">
                            <strong>基础统计:</strong><br>
                            总数: ${data.data.basic.total}<br>
                            活跃: ${data.data.basic.active}<br>
                            非活跃: ${data.data.basic.inactive}
                        </div>
                        <div class="stat-item">
                            <strong>配置的统计项:</strong> ${data.data.statistics.length} 个<br>
                            ${data.data.statistics.map(stat => 
                                `• ${stat.displayName} (${stat.statisticsType})`
                            ).join('<br>')}
                        </div>
                        <div class="stat-item">
                            <strong>数据库信息:</strong><br>
                            代码: ${data.databaseInfo.code}<br>
                            总字段: ${data.databaseInfo.totalFields}<br>
                            统计字段: ${data.databaseInfo.statisticsFields}
                        </div>
                    `;
                } else {
                    container.innerHTML = `<div class="error">❌ 新API失败: ${data.error}</div>`;
                }
            })
            .catch(error => {
                document.getElementById('new-stats').innerHTML = `<div class="error">❌ 新API错误: ${error.message}</div>`;
            });
    </script>
</body>
</html>

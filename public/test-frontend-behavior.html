<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Frontend Behavior</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 3px; }
        button { padding: 8px 16px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .error { color: red; }
        .success { color: green; }
        .warning { color: orange; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .step { margin: 10px 0; padding: 10px; background: #e9ecef; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>前端行为测试</h1>
        
        <div class="test-section">
            <h2>问题重现步骤</h2>
            <div class="step">
                <strong>步骤1:</strong> 用户访问页面，看到初始状态的筛选器计数
            </div>
            <div class="step">
                <strong>步骤2:</strong> 用户选择"器械类别=3"，右侧显示522条数据
            </div>
            <div class="step">
                <strong>步骤3:</strong> 用户查看"医学专科"筛选器，计数正确更新
            </div>
            <div class="step">
                <strong>步骤4:</strong> 用户查看"审查小组"筛选器，计数没有更新（问题）
            </div>
        </div>
        
        <div class="test-section">
            <h2>1. 模拟API调用序列</h2>
            <button onclick="simulateUserActions()">模拟用户操作序列</button>
            <div id="simulation-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h2>2. 检查防抖和时序</h2>
            <button onclick="testDebounceAndTiming()">测试防抖和时序</button>
            <div id="timing-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h2>3. 验证字段配置</h2>
            <button onclick="checkFieldConfigs()">检查字段配置</button>
            <div id="config-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h2>4. 实际前端测试</h2>
            <p>请按以下步骤在实际前端页面中测试：</p>
            <ol>
                <li>打开 <a href="/data/list/us_class" target="_blank">us_class 页面</a></li>
                <li>打开浏览器开发者工具的Network标签</li>
                <li>选择"器械类别=3"</li>
                <li>观察Network标签中是否有以下请求：
                    <ul>
                        <li><code>/api/meta/us_class/dynamic-counts?field=medicalspecialty&filters=...</code></li>
                        <li><code>/api/meta/us_class/dynamic-counts?field=review_panel&filters=...</code></li>
                    </ul>
                </li>
                <li>检查这些请求的响应是否正确</li>
                <li>在Console标签中查看是否有错误或警告</li>
            </ol>
        </div>
    </div>

    <script>
        async function simulateUserActions() {
            const resultDiv = document.getElementById('simulation-result');
            resultDiv.innerHTML = '正在模拟用户操作序列...';
            
            try {
                let html = '<div class="success">✅ 模拟用户操作序列</div>';
                
                // 步骤1: 获取初始状态
                html += '<h4>步骤1: 页面初始加载</h4>';
                const initialRequests = [
                    fetch('/api/meta/us_class/dynamic-counts?field=medicalspecialty&filters=%7B%7D'),
                    fetch('/api/meta/us_class/dynamic-counts?field=review_panel&filters=%7B%7D')
                ];
                
                const [initialMedical, initialReview] = await Promise.all(initialRequests);
                const [initialMedicalData, initialReviewData] = await Promise.all([
                    initialMedical.json(),
                    initialReview.json()
                ]);
                
                html += '<table>';
                html += '<tr><th>字段</th><th>初始状态总计数</th><th>状态</th></tr>';
                html += \`<tr><td>medicalspecialty</td><td>\${initialMedicalData.success ? initialMedicalData.data.reduce((sum, item) => sum + item.count, 0) : 'Error'}</td><td>\${initialMedicalData.success ? '✅' : '❌'}</td></tr>\`;
                html += \`<tr><td>review_panel</td><td>\${initialReviewData.success ? initialReviewData.data.reduce((sum, item) => sum + item.count, 0) : 'Error'}</td><td>\${initialReviewData.success ? '✅' : '❌'}</td></tr>\`;
                html += '</table>';
                
                // 步骤2: 用户选择器械类别=3
                html += '<h4>步骤2: 用户选择器械类别=3</h4>';
                const filteredRequests = [
                    fetch('/api/meta/us_class/dynamic-counts?field=medicalspecialty&filters=%7B%22deviceclass%22%3A%5B%223%22%5D%7D'),
                    fetch('/api/meta/us_class/dynamic-counts?field=review_panel&filters=%7B%22deviceclass%22%3A%5B%223%22%5D%7D')
                ];
                
                const [filteredMedical, filteredReview] = await Promise.all(filteredRequests);
                const [filteredMedicalData, filteredReviewData] = await Promise.all([
                    filteredMedical.json(),
                    filteredReview.json()
                ]);
                
                html += '<table>';
                html += '<tr><th>字段</th><th>筛选后总计数</th><th>状态</th><th>是否正确</th></tr>';
                const medicalTotal = filteredMedicalData.success ? filteredMedicalData.data.reduce((sum, item) => sum + item.count, 0) : 0;
                const reviewTotal = filteredReviewData.success ? filteredReviewData.data.reduce((sum, item) => sum + item.count, 0) : 0;
                html += \`<tr><td>medicalspecialty</td><td>\${medicalTotal}</td><td>\${filteredMedicalData.success ? '✅' : '❌'}</td><td>\${medicalTotal === 522 ? '✅' : '❌'}</td></tr>\`;
                html += \`<tr><td>review_panel</td><td>\${reviewTotal}</td><td>\${filteredReviewData.success ? '✅' : '❌'}</td><td>\${reviewTotal === 522 ? '✅' : '❌'}</td></tr>\`;
                html += '</table>';
                
                // 步骤3: 分析结果
                html += '<h4>步骤3: 分析结果</h4>';
                if (medicalTotal === 522 && reviewTotal === 522) {
                    html += '<div class="success">✅ 两个字段的API都正确返回了522的总计数</div>';
                    html += '<div class="warning">⚠️ 如果前端仍显示错误计数，问题在于前端状态管理</div>';
                } else {
                    html += '<div class="error">❌ API返回的计数不正确</div>';
                }
                
                // 保存数据供其他测试使用
                window.testData = {
                    initialMedicalData,
                    initialReviewData,
                    filteredMedicalData,
                    filteredReviewData
                };
                
                resultDiv.innerHTML = html;
                
            } catch (error) {
                resultDiv.innerHTML = \`<div class="error">❌ 模拟失败: \${error.message}</div>\`;
            }
        }
        
        async function testDebounceAndTiming() {
            const resultDiv = document.getElementById('timing-result');
            resultDiv.innerHTML = '正在测试防抖和时序...';
            
            try {
                let html = '<div class="success">✅ 防抖和时序测试</div>';
                
                // 测试快速连续请求
                html += '<h4>测试快速连续请求</h4>';
                const startTime = Date.now();
                
                // 模拟用户快速选择多个筛选条件
                const rapidRequests = [];
                for (let i = 0; i < 5; i++) {
                    rapidRequests.push(
                        fetch('/api/meta/us_class/dynamic-counts?field=review_panel&filters=%7B%22deviceclass%22%3A%5B%223%22%5D%7D')
                    );
                    // 短暂延迟模拟快速点击
                    await new Promise(resolve => setTimeout(resolve, 50));
                }
                
                const responses = await Promise.all(rapidRequests);
                const results = await Promise.all(responses.map(r => r.json()));
                
                const endTime = Date.now();
                const duration = endTime - startTime;
                
                html += \`<p>发送了5个快速连续请求，耗时: \${duration}ms</p>\`;
                
                const successCount = results.filter(r => r.success).length;
                html += \`<p>成功响应: \${successCount}/5</p>\`;
                
                if (successCount === 5) {
                    html += '<div class="success">✅ 所有请求都成功，没有竞态问题</div>';
                } else {
                    html += '<div class="error">❌ 存在请求失败，可能有竞态问题</div>';
                }
                
                // 检查响应一致性
                const firstResult = results[0];
                const allSame = results.every(r => 
                    r.success && 
                    JSON.stringify(r.data) === JSON.stringify(firstResult.data)
                );
                
                if (allSame) {
                    html += '<div class="success">✅ 所有响应数据一致</div>';
                } else {
                    html += '<div class="warning">⚠️ 响应数据不一致，可能存在缓存或状态问题</div>';
                }
                
                resultDiv.innerHTML = html;
                
            } catch (error) {
                resultDiv.innerHTML = \`<div class="error">❌ 测试失败: \${error.message}</div>\`;
            }
        }
        
        async function checkFieldConfigs() {
            const resultDiv = document.getElementById('config-result');
            resultDiv.innerHTML = '正在检查字段配置...';
            
            try {
                const response = await fetch('/api/config/us_class');
                const result = await response.json();
                
                if (!result.success) {
                    resultDiv.innerHTML = \`<div class="error">❌ 配置获取失败: \${result.error}</div>\`;
                    return;
                }
                
                const medicalConfig = result.fields.find(f => f.fieldName === 'medicalspecialty');
                const reviewConfig = result.fields.find(f => f.fieldName === 'review_panel');
                
                let html = '<div class="success">✅ 字段配置检查</div>';
                html += '<table>';
                html += '<tr><th>属性</th><th>medicalspecialty</th><th>review_panel</th><th>是否一致</th></tr>';
                
                const properties = ['filterType', 'isFilterable', 'isActive', 'sortOrder'];
                properties.forEach(prop => {
                    const medicalValue = medicalConfig?.[prop];
                    const reviewValue = reviewConfig?.[prop];
                    const isEqual = medicalValue === reviewValue;
                    
                    html += \`<tr>\`;
                    html += \`<td>\${prop}</td>\`;
                    html += \`<td>\${medicalValue}</td>\`;
                    html += \`<td>\${reviewValue}</td>\`;
                    html += \`<td>\${isEqual ? '✅' : '❌'}</td>\`;
                    html += \`</tr>\`;
                });
                
                html += '</table>';
                
                // 检查关键配置
                if (medicalConfig?.filterType === 'multi_select' && reviewConfig?.filterType === 'multi_select') {
                    html += '<div class="success">✅ 两个字段都是 multi_select 类型</div>';
                } else {
                    html += '<div class="error">❌ 字段类型配置不一致</div>';
                }
                
                if (medicalConfig?.isFilterable && reviewConfig?.isFilterable) {
                    html += '<div class="success">✅ 两个字段都可筛选</div>';
                } else {
                    html += '<div class="error">❌ 字段筛选配置不一致</div>';
                }
                
                resultDiv.innerHTML = html;
                
            } catch (error) {
                resultDiv.innerHTML = \`<div class="error">❌ 检查失败: \${error.message}</div>\`;
            }
        }
        
        // 页面加载时的提示
        window.onload = function() {
            console.log('🔍 前端行为测试页面已加载');
            console.log('请按顺序运行测试，然后在实际页面中验证结果');
        };
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Filter Issue</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1000px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 3px; }
        button { padding: 8px 16px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .error { color: red; }
        .success { color: green; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="container">
        <h1>筛选器动态计数调试</h1>
        
        <div class="test-section">
            <h2>问题描述</h2>
            <p>用户报告：选择器械类别=3后，医学专科的计数能正确更新，但审查小组的计数没有更新。</p>
            <p>两个字段都是 multi_select 类型，应该有相同的行为。</p>
        </div>
        
        <div class="test-section">
            <h2>1. 验证数据库数据</h2>
            <button onclick="testDatabaseData()">检查数据库数据</button>
            <div id="database-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h2>2. 测试API响应</h2>
            <button onclick="testAPIResponses()">测试两个字段的API</button>
            <div id="api-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h2>3. 对比分析</h2>
            <button onclick="compareFields()">对比字段差异</button>
            <div id="compare-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h2>4. 前端调试指南</h2>
            <p>请打开浏览器开发者工具，然后：</p>
            <ol>
                <li>访问 <a href="/data/list/us_class" target="_blank">us_class 页面</a></li>
                <li>在Console中查找以下日志：</li>
                <ul>
                    <li>🚀 初始化动态计数，筛选字段: [...]</li>
                    <li>📡 获取 review_panel 的动态计数</li>
                    <li>📡 获取 medicalspecialty 的动态计数</li>
                    <li>✅ review_panel 动态计数更新: [...]</li>
                    <li>✅ medicalspecialty 动态计数更新: [...]</li>
                    <li>🔍 review_panel 渲染状态: {...}</li>
                    <li>🔍 medicalspecialty 渲染状态: {...}</li>
                </ul>
                <li>选择器械类别=3，观察是否有新的动态计数请求</li>
                <li>检查Network标签，确认是否发送了dynamic-counts请求</li>
            </ol>
        </div>
    </div>

    <script>
        async function testDatabaseData() {
            const resultDiv = document.getElementById('database-result');
            resultDiv.innerHTML = '正在检查数据库数据...';
            
            try {
                // 检查器械类别=3的总数
                const totalResponse = await fetch('/api/data/us_class?deviceclass=3&limit=1');
                const totalResult = await totalResponse.json();
                
                if (totalResult.success) {
                    const totalCount = totalResult.pagination.total;
                    
                    resultDiv.innerHTML = `
                        <div class="success">✅ 数据库查询成功</div>
                        <p><strong>器械类别=3的总记录数:</strong> ${totalCount}</p>
                        <p>这个数字应该与前端筛选后的记录数一致。</p>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ 数据库查询失败: ${totalResult.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 网络错误: ${error.message}</div>`;
            }
        }
        
        async function testAPIResponses() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.innerHTML = '正在测试API响应...';
            
            try {
                // 测试两个字段的动态计数API
                const [medicalResponse, reviewResponse] = await Promise.all([
                    fetch('/api/meta/us_class/dynamic-counts?field=medicalspecialty&filters=%7B%22deviceclass%22%3A%5B%223%22%5D%7D'),
                    fetch('/api/meta/us_class/dynamic-counts?field=review_panel&filters=%7B%22deviceclass%22%3A%5B%223%22%5D%7D')
                ]);
                
                const [medicalResult, reviewResult] = await Promise.all([
                    medicalResponse.json(),
                    reviewResponse.json()
                ]);
                
                let html = '<div class="success">✅ API测试完成</div>';
                
                if (medicalResult.success && reviewResult.success) {
                    const medicalTotal = medicalResult.data.reduce((sum, item) => sum + item.count, 0);
                    const reviewTotal = reviewResult.data.reduce((sum, item) => sum + item.count, 0);
                    
                    html += `
                        <table>
                            <tr><th>字段</th><th>选项数量</th><th>总计数</th><th>前3个选项</th></tr>
                            <tr>
                                <td>medicalspecialty</td>
                                <td>${medicalResult.data.length}</td>
                                <td>${medicalTotal}</td>
                                <td>${medicalResult.data.slice(0, 3).map(item => \`\${item.value}(\${item.count})\`).join(', ')}</td>
                            </tr>
                            <tr>
                                <td>review_panel</td>
                                <td>${reviewResult.data.length}</td>
                                <td>${reviewTotal}</td>
                                <td>${reviewResult.data.slice(0, 3).map(item => \`\${item.value}(\${item.count})\`).join(', ')}</td>
                            </tr>
                        </table>
                    `;
                    
                    if (medicalTotal === reviewTotal) {
                        html += '<div class="success">✅ 两个字段的总计数一致，API工作正常</div>';
                    } else {
                        html += '<div class="error">❌ 两个字段的总计数不一致，可能有问题</div>';
                    }
                } else {
                    html += '<div class="error">❌ API请求失败</div>';
                    if (!medicalResult.success) html += \`<p>medicalspecialty错误: \${medicalResult.error}</p>\`;
                    if (!reviewResult.success) html += \`<p>review_panel错误: \${reviewResult.error}</p>\`;
                }
                
                resultDiv.innerHTML = html;
                
                // 保存结果供后续使用
                window.testResults = { medicalResult, reviewResult };
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 网络错误: ${error.message}</div>`;
            }
        }
        
        async function compareFields() {
            const resultDiv = document.getElementById('compare-result');
            
            if (!window.testResults) {
                resultDiv.innerHTML = '<div class="error">❌ 请先运行API测试</div>';
                return;
            }
            
            const { medicalResult, reviewResult } = window.testResults;
            
            let html = '<div class="success">📊 字段对比分析</div>';
            
            // 检查字段配置
            try {
                const configResponse = await fetch('/api/config/us_class');
                const configResult = await configResponse.json();
                
                if (configResult.success) {
                    const medicalConfig = configResult.fields.find(f => f.fieldName === 'medicalspecialty');
                    const reviewConfig = configResult.fields.find(f => f.fieldName === 'review_panel');
                    
                    html += `
                        <h4>字段配置对比:</h4>
                        <table>
                            <tr><th>属性</th><th>medicalspecialty</th><th>review_panel</th><th>是否一致</th></tr>
                            <tr>
                                <td>filterType</td>
                                <td>\${medicalConfig?.filterType}</td>
                                <td>\${reviewConfig?.filterType}</td>
                                <td>\${medicalConfig?.filterType === reviewConfig?.filterType ? '✅' : '❌'}</td>
                            </tr>
                            <tr>
                                <td>isFilterable</td>
                                <td>\${medicalConfig?.isFilterable}</td>
                                <td>\${reviewConfig?.isFilterable}</td>
                                <td>\${medicalConfig?.isFilterable === reviewConfig?.isFilterable ? '✅' : '❌'}</td>
                            </tr>
                            <tr>
                                <td>isActive</td>
                                <td>\${medicalConfig?.isActive}</td>
                                <td>\${reviewConfig?.isActive}</td>
                                <td>\${medicalConfig?.isActive === reviewConfig?.isActive ? '✅' : '❌'}</td>
                            </tr>
                        </table>
                    `;
                }
            } catch (error) {
                html += \`<p class="error">配置获取失败: \${error.message}</p>\`;
            }
            
            html += `
                <h4>API响应对比:</h4>
                <table>
                    <tr><th>属性</th><th>medicalspecialty</th><th>review_panel</th><th>说明</th></tr>
                    <tr>
                        <td>API成功</td>
                        <td>\${medicalResult.success ? '✅' : '❌'}</td>
                        <td>\${reviewResult.success ? '✅' : '❌'}</td>
                        <td>两个API都应该成功</td>
                    </tr>
                    <tr>
                        <td>选项数量</td>
                        <td>\${medicalResult.data?.length || 0}</td>
                        <td>\${reviewResult.data?.length || 0}</td>
                        <td>数量可能不同，这是正常的</td>
                    </tr>
                    <tr>
                        <td>总计数</td>
                        <td>\${medicalResult.data?.reduce((sum, item) => sum + item.count, 0) || 0}</td>
                        <td>\${reviewResult.data?.reduce((sum, item) => sum + item.count, 0) || 0}</td>
                        <td>应该相等，都等于器械类别=3的总数</td>
                    </tr>
                </table>
            `;
            
            html += `
                <h4>可能的问题:</h4>
                <ul>
                    <li>如果API都正常，问题可能在前端状态管理</li>
                    <li>检查前端是否正确发送了review_panel的动态计数请求</li>
                    <li>检查前端是否正确使用了返回的动态计数数据</li>
                    <li>可能存在缓存问题或状态更新时序问题</li>
                </ul>
            `;
            
            resultDiv.innerHTML = html;
        }
        
        // 页面加载时的提示
        window.onload = function() {
            console.log('🔍 筛选器调试页面已加载');
            console.log('请按顺序点击测试按钮，然后查看前端页面的调试日志');
        };
    </script>
</body>
</html>

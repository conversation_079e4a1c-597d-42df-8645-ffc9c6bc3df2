<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Filter Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 3px; }
        button { padding: 8px 16px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <div class="container">
        <h1>筛选器联动测试</h1>
        
        <div class="test-section">
            <h2>1. 测试初始状态动态计数</h2>
            <button onclick="testInitialCounts()">获取初始计数</button>
            <div id="initial-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h2>2. 测试筛选后动态计数</h2>
            <button onclick="testFilteredCounts()">获取筛选后计数</button>
            <div id="filtered-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h2>3. 对比结果</h2>
            <button onclick="compareResults()">对比分析</button>
            <div id="compare-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h2>4. 测试前端页面</h2>
            <p>请打开浏览器开发者工具，然后点击下面的链接：</p>
            <a href="/data/list/us_class" target="_blank">打开 us_class 页面</a>
            <p>在控制台中查找以下调试信息：</p>
            <ul>
                <li>🚀 Initializing dynamic counts for all filterable fields</li>
                <li>✅ Dynamic counts updated for thirdpartyflag</li>
                <li>🔍 Field thirdpartyflag: (查看详细数据)</li>
            </ul>
        </div>
    </div>

    <script>
        let initialData = null;
        let filteredData = null;
        
        async function testInitialCounts() {
            const resultDiv = document.getElementById('initial-result');
            resultDiv.innerHTML = '正在获取初始计数...';
            
            try {
                const response = await fetch('/api/meta/us_class/dynamic-counts?field=thirdpartyflag&filters=%7B%7D');
                const result = await response.json();
                
                if (result.success) {
                    initialData = result.data;
                    resultDiv.innerHTML = `
                        <div class="success">✅ 初始计数获取成功</div>
                        <pre>${JSON.stringify(result.data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ 获取失败: ${result.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 网络错误: ${error.message}</div>`;
            }
        }
        
        async function testFilteredCounts() {
            const resultDiv = document.getElementById('filtered-result');
            resultDiv.innerHTML = '正在获取筛选后计数...';
            
            try {
                const response = await fetch('/api/meta/us_class/dynamic-counts?field=thirdpartyflag&filters=%7B%22deviceclass%22%3A%5B%222%22%5D%7D');
                const result = await response.json();
                
                if (result.success) {
                    filteredData = result.data;
                    resultDiv.innerHTML = `
                        <div class="success">✅ 筛选后计数获取成功</div>
                        <pre>${JSON.stringify(result.data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ 获取失败: ${result.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 网络错误: ${error.message}</div>`;
            }
        }
        
        function compareResults() {
            const resultDiv = document.getElementById('compare-result');
            
            if (!initialData || !filteredData) {
                resultDiv.innerHTML = '<div class="error">❌ 请先获取初始计数和筛选后计数</div>';
                return;
            }
            
            const initialTotal = initialData.reduce((sum, item) => sum + item.count, 0);
            const filteredTotal = filteredData.reduce((sum, item) => sum + item.count, 0);
            
            let comparison = `
                <div class="success">📊 对比分析结果</div>
                <p><strong>初始状态总计:</strong> ${initialTotal} 条</p>
                <p><strong>筛选后总计:</strong> ${filteredTotal} 条</p>
                <p><strong>筛选是否生效:</strong> ${filteredTotal < initialTotal ? '✅ 是' : '❌ 否'}</p>
                <h4>详细对比:</h4>
                <table border="1" style="border-collapse: collapse; width: 100%;">
                    <tr><th>值</th><th>初始计数</th><th>筛选后计数</th><th>变化</th></tr>
            `;
            
            const initialMap = new Map(initialData.map(item => [item.value, item.count]));
            const filteredMap = new Map(filteredData.map(item => [item.value, item.count]));
            
            const allValues = new Set([...initialMap.keys(), ...filteredMap.keys()]);
            
            allValues.forEach(value => {
                const initialCount = initialMap.get(value) || 0;
                const filteredCount = filteredMap.get(value) || 0;
                const change = filteredCount - initialCount;
                const changeStr = change > 0 ? `+${change}` : change.toString();
                
                comparison += `
                    <tr>
                        <td>${value}</td>
                        <td>${initialCount}</td>
                        <td>${filteredCount}</td>
                        <td>${changeStr}</td>
                    </tr>
                `;
            });
            
            comparison += '</table>';
            
            if (filteredTotal === 3567) {
                comparison += '<div class="success">✅ 筛选后总计符合预期 (3567条)</div>';
            } else {
                comparison += '<div class="error">❌ 筛选后总计不符合预期，应该是3567条</div>';
            }
            
            resultDiv.innerHTML = comparison;
        }
        
        // 页面加载时自动测试
        window.onload = function() {
            console.log('🔍 筛选器联动测试页面已加载');
            console.log('请点击按钮进行测试，或直接访问 /data/list/us_class 页面');
        };
    </script>
</body>
</html>

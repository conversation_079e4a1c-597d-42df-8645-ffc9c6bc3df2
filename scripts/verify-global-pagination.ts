import { GLOBAL_PAGINATION_CONFIG, validatePaginationParams, buildPaginationResponse } from '../src/lib/globalPagination';

async function verifyGlobalPagination() {
  console.log('🔍 验证全局翻页配置完整性...\n');

  // 1. 验证全局配置
  console.log('📋 全局翻页配置:');
  console.log(`   默认每页条数: ${GLOBAL_PAGINATION_CONFIG.DEFAULT_PAGE_SIZE}`);
  console.log(`   最大每页条数: ${GLOBAL_PAGINATION_CONFIG.MAX_PAGE_SIZE}`);
  console.log(`   最大翻页页数: ${GLOBAL_PAGINATION_CONFIG.MAX_PAGES}`);

  // 2. 测试API端点
  console.log('\n🌐 测试API端点...');
  
  const testEndpoints = [
    'http://localhost:3000/api/data/us_pmn?page=1&limit=20',
    'http://localhost:3000/api/data/us_class?page=1&limit=20',
  ];

  for (const endpoint of testEndpoints) {
    try {
      console.log(`\n📡 测试: ${endpoint}`);
      const response = await fetch(endpoint);
      
      if (!response.ok) {
        console.log(`❌ 请求失败: ${response.status}`);
        continue;
      }

      const data = await response.json();
      const pagination = data.pagination;

      if (pagination) {
        console.log(`✅ API响应正常`);
        console.log(`   maxPages: ${pagination.maxPages} (应该是100)`);
        console.log(`   isAtMaxPages: ${pagination.isAtMaxPages}`);
        console.log(`   totalCount: ${pagination.totalCount}`);
        
        // 验证是否使用了全局配置
        if (pagination.maxPages === GLOBAL_PAGINATION_CONFIG.MAX_PAGES) {
          console.log(`✅ 使用了全局配置`);
        } else {
          console.log(`❌ 没有使用全局配置，maxPages=${pagination.maxPages}`);
        }
      } else {
        console.log(`❌ 没有分页信息`);
      }
    } catch (error) {
      console.log(`❌ 请求错误: ${error.message}`);
    }
  }

  // 3. 测试高级搜索API
  console.log('\n🔍 测试高级搜索API...');
  
  try {
    const searchResponse = await fetch('http://localhost:3000/api/advanced-search/us_pmn', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        conditions: [],
        page: 1,
        limit: 20
      })
    });

    if (searchResponse.ok) {
      const searchData = await searchResponse.json();
      const pagination = searchData.pagination;
      
      console.log(`✅ 高级搜索API响应正常`);
      console.log(`   maxPages: ${pagination.maxPages} (应该是100)`);
      console.log(`   isAtMaxPages: ${pagination.isAtMaxPages}`);
      
      if (pagination.maxPages === GLOBAL_PAGINATION_CONFIG.MAX_PAGES) {
        console.log(`✅ 高级搜索使用了全局配置`);
      } else {
        console.log(`❌ 高级搜索没有使用全局配置`);
      }
    } else {
      console.log(`❌ 高级搜索请求失败: ${searchResponse.status}`);
    }
  } catch (error) {
    console.log(`❌ 高级搜索请求错误: ${error.message}`);
  }

  // 4. 测试边界情况
  console.log('\n🧪 测试边界情况...');
  
  const boundaryTests = [
    { page: 100, limit: 20, desc: '第100页（边界）' },
    { page: 101, limit: 20, desc: '第101页（超限）' },
    { page: 50, limit: 150, desc: '超大每页条数' },
  ];

  boundaryTests.forEach(test => {
    const result = validatePaginationParams(test.page, test.limit);
    console.log(`\n   ${test.desc}:`);
    console.log(`     输入: page=${test.page}, limit=${test.limit}`);
    console.log(`     输出: page=${result.page}, limit=${result.limit}`);
    console.log(`     是否达到限制: ${result.isAtMaxPages}`);
  });

  // 5. 性能测试
  console.log('\n⚡ 性能测试...');
  
  const iterations = 1000;
  const startTime = performance.now();
  
  for (let i = 0; i < iterations; i++) {
    validatePaginationParams(
      Math.floor(Math.random() * 150), 
      Math.floor(Math.random() * 200)
    );
  }
  
  const endTime = performance.now();
  const avgTime = (endTime - startTime) / iterations;
  
  console.log(`   ${iterations}次参数验证:`);
  console.log(`   平均时间: ${avgTime.toFixed(4)}ms/次`);
  console.log(`   QPS: ${(1000 / avgTime).toFixed(0)} 操作/秒`);

  // 6. 总结
  console.log('\n📊 验证总结:');
  console.log(`✅ 全局配置: MAX_PAGES = ${GLOBAL_PAGINATION_CONFIG.MAX_PAGES}`);
  console.log(`✅ 性能优化: 无数据库查询开销`);
  console.log(`✅ 统一体验: 所有API使用相同限制`);
  console.log(`✅ 用户友好: 100页限制覆盖更多使用场景`);

  console.log('\n🎯 建议:');
  console.log('   • 100页限制适合大多数用户需求');
  console.log('   • 如需更多数据，建议使用搜索功能');
  console.log('   • 监控用户是否经常达到100页限制');
}

// 运行验证
if (typeof window === 'undefined') {
  verifyGlobalPagination().catch(console.error);
} else {
  console.log('请在Node.js环境中运行此脚本');
}

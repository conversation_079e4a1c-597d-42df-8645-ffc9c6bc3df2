#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 最终修复 us_pmn 数据库配置
 * 确保所有配置都是最新和正确的
 */

async function finalUsPmnFix() {
  console.log('🔧 最终修复 us_pmn 数据库配置...\n');

  try {
    // 1. 确保数据库配置正确
    console.log('📋 1. 更新数据库配置...');
    const dbConfig = await db.databaseConfig.upsert({
      where: { code: 'us_pmn' },
      update: {
        name: '美国PMN(510k)',
        category: '全球器械',
        description: '美国FDA PMN(510k)医疗器械审批信息',
        accessLevel: 'free',
        tableName: 'us_pmn',
        modelName: 'uSPremarketNotification',
        isActive: true,
        defaultSort: [
          { field: 'decisiondate', order: 'desc' },
          { field: 'id', order: 'asc' }
        ],
        sortOrder: 200
      },
      create: {
        code: 'us_pmn',
        name: '美国PMN(510k)',
        category: '全球器械',
        description: '美国FDA PMN(510k)医疗器械审批信息',
        accessLevel: 'free',
        tableName: 'us_pmn',
        modelName: 'uSPremarketNotification',
        isActive: true,
        defaultSort: [
          { field: 'decisiondate', order: 'desc' },
          { field: 'id', order: 'asc' }
        ],
        sortOrder: 200
      }
    });
    console.log('✅ 数据库配置已更新');

    // 2. 确保关键字段配置正确
    console.log('\n📊 2. 更新关键字段配置...');
    
    const keyFields = [
      {
        fieldName: 'knumber',
        displayName: 'K Number',
        fieldType: 'text',
        isVisible: true,
        isSearchable: true,
        isFilterable: true,
        isSortable: true,
        listOrder: 1,
        sortOrder: 1,
        detailOrder: 2,
        searchType: 'contains',
        filterType: 'input'
      },
      {
        fieldName: 'devicename',
        displayName: 'Device Name',
        fieldType: 'text',
        isVisible: true,
        isSearchable: true,
        isFilterable: false,
        isSortable: true,
        listOrder: 2,
        sortOrder: 2,
        detailOrder: 23,
        searchType: 'contains',
        filterType: 'input'
      },
      {
        fieldName: 'applicant',
        displayName: 'Applicant',
        fieldType: 'text',
        isVisible: true,
        isSearchable: true,
        isFilterable: true,
        isSortable: true,
        listOrder: 3,
        sortOrder: 3,
        detailOrder: 3,
        searchType: 'contains',
        filterType: 'select'
      },
      {
        fieldName: 'datereceived',
        displayName: 'Date Received',
        fieldType: 'date',
        isVisible: true,
        isSearchable: false,
        isFilterable: true,
        isSortable: true,
        listOrder: 4,
        sortOrder: 4,
        detailOrder: 12,
        searchType: 'date_range',
        filterType: 'date_range'
      },
      {
        fieldName: 'decisiondate',
        displayName: 'Decision Date',
        fieldType: 'date',
        isVisible: true,
        isSearchable: false,
        isFilterable: true,
        isSortable: true,
        listOrder: 5,
        sortOrder: 5,
        detailOrder: 13,
        searchType: 'date_range',
        filterType: 'date_range'
      },
      {
        fieldName: 'decision',
        displayName: 'Decision',
        fieldType: 'text',
        isVisible: true,
        isSearchable: true,
        isFilterable: true,
        isSortable: true,
        listOrder: 6,
        sortOrder: 6,
        detailOrder: 14,
        searchType: 'contains',
        filterType: 'select'
      },
      {
        fieldName: 'productcode',
        displayName: 'Product Code',
        fieldType: 'text',
        isVisible: true,
        isSearchable: true,
        isFilterable: true,
        isSortable: true,
        listOrder: 7,
        sortOrder: 7,
        detailOrder: 16,
        searchType: 'contains',
        filterType: 'select'
      },
      {
        fieldName: 'type',
        displayName: 'Type',
        fieldType: 'text',
        isVisible: true,
        isSearchable: true,
        isFilterable: true,
        isSortable: true,
        listOrder: 8,
        sortOrder: 8,
        detailOrder: 20,
        searchType: 'contains',
        filterType: 'select'
      }
    ];

    for (const field of keyFields) {
      await db.fieldConfig.upsert({
        where: {
          databaseCode_fieldName: {
            databaseCode: 'us_pmn',
            fieldName: field.fieldName
          }
        },
        update: {
          displayName: field.displayName,
          fieldType: field.fieldType as any,
          isVisible: field.isVisible,
          isSearchable: field.isSearchable,
          isFilterable: field.isFilterable,
          isSortable: field.isSortable,
          listOrder: field.listOrder,
          sortOrder: field.sortOrder,
          detailOrder: field.detailOrder,
          searchType: field.searchType as any,
          filterType: field.filterType as any,
          isActive: true
        },
        create: {
          databaseCode: 'us_pmn',
          fieldName: field.fieldName,
          displayName: field.displayName,
          fieldType: field.fieldType as any,
          isVisible: field.isVisible,
          isSearchable: field.isSearchable,
          isFilterable: field.isFilterable,
          isSortable: field.isSortable,
          listOrder: field.listOrder,
          sortOrder: field.sortOrder,
          detailOrder: field.detailOrder,
          searchType: field.searchType as any,
          filterType: field.filterType as any,
          isActive: true
        }
      });
      console.log(`✅ 更新字段: ${field.fieldName} (${field.displayName})`);
    }

    // 3. 验证配置
    console.log('\n📊 3. 验证最终配置...');
    
    const visibleFields = await db.fieldConfig.findMany({
      where: {
        databaseCode: 'us_pmn',
        isVisible: true,
        isActive: true
      },
      select: {
        fieldName: true,
        displayName: true,
        listOrder: true,
        sortOrder: true
      },
      orderBy: { listOrder: 'asc' }
    });

    console.log(`✅ 可见字段 (${visibleFields.length} 个):`);
    visibleFields.forEach((field, index) => {
      console.log(`   ${index + 1}. ${field.fieldName} (${field.displayName}) - listOrder: ${field.listOrder}`);
    });

    const filterableFields = await db.fieldConfig.findMany({
      where: {
        databaseCode: 'us_pmn',
        isFilterable: true,
        isActive: true
      },
      select: {
        fieldName: true,
        displayName: true,
        filterType: true,
        isVisible: true
      },
      orderBy: { fieldName: 'asc' }
    });

    console.log(`\n🔍 可筛选字段 (${filterableFields.length} 个):`);
    filterableFields.forEach((field, index) => {
      const visibility = field.isVisible ? '可见' : '隐藏';
      console.log(`   ${index + 1}. ${field.fieldName} (${field.displayName}) - ${field.filterType} [${visibility}]`);
    });

    // 4. 测试 API
    console.log('\n🌐 4. 测试 API 响应...');
    try {
      const response = await fetch('http://localhost:3000/api/data/us_pmn?page=1&limit=1');
      const result = await response.json();
      
      if (result.success) {
        console.log('✅ API 测试成功');
        console.log(`   数据条数: ${result.data?.length || 0}`);
        console.log(`   总记录数: ${result.pagination?.totalCount || 0}`);
        
        if (result.config?.fields) {
          const visibleApiFields = result.config.fields.filter((f: any) => f.isVisible);
          console.log(`   API 返回可见字段: ${visibleApiFields.map((f: any) => f.fieldName).join(', ')}`);
        }
      } else {
        console.log('❌ API 测试失败:', result.error);
      }
    } catch (error) {
      console.log('❌ API 请求失败:', error.message);
    }

    console.log('\n✅ us_pmn 数据库配置修复完成！');
    console.log('\n💡 下一步操作建议:');
    console.log('   1. 重启开发服务器: npm run dev');
    console.log('   2. 清除浏览器缓存');
    console.log('   3. 访问: http://localhost:3000/data/list/us_pmn');
    console.log('   4. 检查页面是否正常显示列表和筛选项');

  } catch (error) {
    console.error('❌ 修复过程中发生错误:', error);
  } finally {
    await db.$disconnect();
  }
}

// 执行修复
finalUsPmnFix()
  .then(() => {
    console.log('\n✨ 修复脚本执行完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 修复脚本执行失败:', error);
    process.exit(1);
  });

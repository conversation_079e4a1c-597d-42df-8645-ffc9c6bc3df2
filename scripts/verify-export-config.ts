#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 验证数据库中的导出配置
 */

async function verifyExportConfig() {
  console.log('🔍 验证数据库中的导出配置...\n');

  try {
    // 1. 查看所有数据库的导出配置
    console.log('📋 所有数据库的导出配置:');
    
    const allConfigs = await db.databaseConfig.findMany({
      where: { isActive: true },
      select: {
        code: true,
        name: true,
        maxExportLimit: true,
        defaultExportLimit: true,
        exportConfig: true,
        accessLevel: true,
      },
      orderBy: { code: 'asc' }
    });

    if (allConfigs.length === 0) {
      console.log('  ❌ 未找到任何数据库配置');
      return;
    }

    allConfigs.forEach((config, index) => {
      console.log(`\n  ${index + 1}. ${config.code} (${config.name})`);
      console.log(`     访问级别: ${config.accessLevel}`);
      console.log(`     最大导出: ${config.maxExportLimit || '未设置'} 条`);
      console.log(`     默认导出: ${config.defaultExportLimit || '未设置'} 条`);
      
      if (config.exportConfig) {
        const exportConfig = typeof config.exportConfig === 'string' 
          ? JSON.parse(config.exportConfig) 
          : config.exportConfig;
        console.log(`     支持格式: ${exportConfig.formats?.join(', ') || '未设置'}`);
        console.log(`     允许大量导出: ${exportConfig.allowLargeExport ? '是' : '否'}`);
        console.log(`     描述: ${exportConfig.description || '未设置'}`);
      } else {
        console.log('     导出配置: 未设置');
      }
    });

    // 2. 检查字段是否正确添加
    console.log('\n🔍 检查表结构...');
    
    const tableInfo = await db.$queryRaw`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'DatabaseConfig' 
      AND column_name IN ('maxExportLimit', 'defaultExportLimit', 'exportConfig')
      ORDER BY column_name;
    ` as any[];

    if (tableInfo.length > 0) {
      console.log('  📋 导出相关字段:');
      tableInfo.forEach((col: any) => {
        console.log(`    - ${col.column_name}: ${col.data_type} (默认: ${col.column_default || 'NULL'})`);
      });
    } else {
      console.log('  ❌ 未找到导出相关字段');
    }

    // 3. 统计配置情况
    console.log('\n📊 配置统计:');
    
    const stats = await db.databaseConfig.groupBy({
      by: ['accessLevel'],
      where: { isActive: true },
      _count: true,
      _avg: {
        maxExportLimit: true,
        defaultExportLimit: true,
      },
    });

    stats.forEach(stat => {
      console.log(`  ${stat.accessLevel}: ${stat._count} 个数据库`);
      console.log(`    平均最大导出: ${Math.round(stat._avg.maxExportLimit || 0)} 条`);
      console.log(`    平均默认导出: ${Math.round(stat._avg.defaultExportLimit || 0)} 条`);
    });

    // 4. 检查是否有未配置导出限制的数据库
    console.log('\n⚠️  检查未配置导出限制的数据库:');
    
    const unconfigured = await db.databaseConfig.findMany({
      where: {
        isActive: true,
        OR: [
          { maxExportLimit: null },
          { defaultExportLimit: null },
        ],
      },
      select: {
        code: true,
        name: true,
        maxExportLimit: true,
        defaultExportLimit: true,
      },
    });

    if (unconfigured.length > 0) {
      console.log(`  找到 ${unconfigured.length} 个未完全配置的数据库:`);
      unconfigured.forEach(config => {
        console.log(`    - ${config.code}: 最大=${config.maxExportLimit || '未设置'}, 默认=${config.defaultExportLimit || '未设置'}`);
      });
    } else {
      console.log('  ✅ 所有数据库都已配置导出限制');
    }

    console.log('\n✅ 导出配置验证完成！');

  } catch (error) {
    console.error('❌ 验证过程中出错:', error);
  } finally {
    await db.$disconnect();
  }
}

// 运行验证
if (require.main === module) {
  verifyExportConfig();
}

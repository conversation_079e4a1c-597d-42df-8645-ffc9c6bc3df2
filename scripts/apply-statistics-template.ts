#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';
import { 
  STATISTICS_TEMPLATES, 
  getRecommendedStatisticsTemplate,
  applyStatisticsTemplate,
  StatisticsTemplate 
} from '../src/lib/statistics-templates';

/**
 * 通用统计配置脚本
 * 可以为任何数据库应用统计模板
 */

interface ScriptOptions {
  databaseCode: string;
  templateName?: keyof typeof STATISTICS_TEMPLATES;
  dryRun?: boolean;
  force?: boolean;
}

async function applyStatisticsTemplateToDatabase(options: ScriptOptions) {
  const { databaseCode, templateName, dryRun = false, force = false } = options;
  
  console.log(`📊 为数据库 "${databaseCode}" 应用统计模板...`);
  
  try {
    // 1. 检查数据库配置是否存在
    const databaseConfig = await db.databaseConfig.findUnique({
      where: { code: databaseCode }
    });
    
    if (!databaseConfig) {
      throw new Error(`数据库配置 "${databaseCode}" 不存在`);
    }
    
    console.log(`✅ 找到数据库配置: ${databaseConfig.name}`);

    // 2. 获取现有字段配置
    const existingFields = await db.fieldConfig.findMany({
      where: {
        databaseCode,
        isActive: true,
      },
      select: {
        fieldName: true,
        displayName: true,
        isStatisticsEnabled: true,
      }
    });

    if (existingFields.length === 0) {
      throw new Error(`数据库 "${databaseCode}" 没有配置任何字段`);
    }

    console.log(`📋 找到 ${existingFields.length} 个字段配置`);

    // 3. 选择统计模板
    let template: StatisticsTemplate;
    if (templateName && STATISTICS_TEMPLATES[templateName]) {
      template = STATISTICS_TEMPLATES[templateName];
      console.log(`🎯 使用指定模板: ${template.name}`);
    } else {
      template = getRecommendedStatisticsTemplate(databaseCode);
      console.log(`🤖 使用推荐模板: ${template.name}`);
    }

    console.log(`📝 模板描述: ${template.description}`);

    // 4. 应用模板到现有字段
    const fieldNames = existingFields.map(f => f.fieldName);
    const applicableConfigs = applyStatisticsTemplate(template, fieldNames);

    console.log(`\n🔧 可应用的统计配置 (${applicableConfigs.length} 个):`);
    applicableConfigs.forEach((config, index) => {
      console.log(`   ${index + 1}. ${config.fieldName} -> ${config.statisticsDisplayName} (${config.statisticsType})`);
    });

    // 5. 检查是否有字段已经启用了统计
    const enabledStats = existingFields.filter(f => f.isStatisticsEnabled);
    if (enabledStats.length > 0 && !force) {
      console.log(`\n⚠️  发现 ${enabledStats.length} 个字段已启用统计:`);
      enabledStats.forEach(field => {
        console.log(`   - ${field.fieldName} (${field.displayName})`);
      });
      console.log('   使用 --force 参数覆盖现有配置');
      
      if (!dryRun) {
        return;
      }
    }

    // 6. 应用配置
    if (dryRun) {
      console.log('\n🔍 预览模式 - 不会实际修改数据库');
    } else {
      console.log('\n💾 应用统计配置...');
      
      for (const config of applicableConfigs) {
        try {
          const result = await db.fieldConfig.updateMany({
            where: {
              databaseCode,
              fieldName: config.fieldName,
              isActive: true,
            },
            data: {
              isStatisticsEnabled: config.isStatisticsEnabled,
              statisticsOrder: config.statisticsOrder,
              statisticsType: config.statisticsType as any,
              statisticsDisplayName: config.statisticsDisplayName,
              statisticsConfig: config.statisticsConfig,
            },
          });

          if (result.count > 0) {
            console.log(`  ✅ ${config.fieldName} -> ${config.statisticsDisplayName}`);
          } else {
            console.log(`  ⚠️  ${config.fieldName} - 未找到匹配的字段`);
          }
        } catch (error) {
          console.error(`  ❌ ${config.fieldName} - 更新失败:`, error);
        }
      }
    }

    // 7. 验证结果
    if (!dryRun) {
      console.log('\n🔍 验证配置结果...');
      const finalStats = await db.fieldConfig.findMany({
        where: {
          databaseCode,
          isStatisticsEnabled: true,
          isActive: true,
        },
        select: {
          fieldName: true,
          statisticsDisplayName: true,
          statisticsType: true,
          statisticsOrder: true,
        },
        orderBy: { statisticsOrder: 'asc' },
      });

      console.log(`\n📊 最终统计配置 (${finalStats.length} 个):`);
      finalStats.forEach((stat, index) => {
        console.log(`   ${index + 1}. ${stat.fieldName} -> ${stat.statisticsDisplayName} (${stat.statisticsType})`);
      });
    }

    // 8. 提供使用指南
    console.log('\n💡 使用指南:');
    console.log(`   1. 新的统计API: /api/stats/${databaseCode}/configurable`);
    console.log(`   2. 前端组件: ConfigurableStatsPanel`);
    console.log(`   3. 原有统计API仍然可用: /api/stats/${databaseCode}`);

  } catch (error) {
    console.error('❌ 应用统计模板失败:', error);
    throw error;
  } finally {
    await db.$disconnect();
  }
}

// 解析命令行参数
function parseArgs(): ScriptOptions {
  const args = process.argv.slice(2);
  const options: ScriptOptions = {
    databaseCode: '',
  };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    
    if (arg === '--database' || arg === '-d') {
      options.databaseCode = args[++i];
    } else if (arg === '--template' || arg === '-t') {
      options.templateName = args[++i] as keyof typeof STATISTICS_TEMPLATES;
    } else if (arg === '--dry-run') {
      options.dryRun = true;
    } else if (arg === '--force') {
      options.force = true;
    } else if (!options.databaseCode) {
      options.databaseCode = arg;
    }
  }

  return options;
}

// 显示帮助信息
function showHelp() {
  console.log(`
📊 统计模板应用工具

用法:
  tsx scripts/apply-statistics-template.ts <database_code> [options]

参数:
  database_code          数据库代码 (必需)

选项:
  -t, --template <name>  指定模板名称 (可选)
  --dry-run             预览模式，不实际修改数据库
  --force               强制覆盖现有统计配置
  -h, --help            显示帮助信息

可用模板:
  ${Object.keys(STATISTICS_TEMPLATES).map(key => `- ${key}`).join('\n  ')}

示例:
  tsx scripts/apply-statistics-template.ts us_class
  tsx scripts/apply-statistics-template.ts us_pmn --template medical_device
  tsx scripts/apply-statistics-template.ts us_class --dry-run
  tsx scripts/apply-statistics-template.ts us_class --force
`);
}

// 主函数
async function main() {
  const options = parseArgs();

  if (!options.databaseCode || process.argv.includes('--help') || process.argv.includes('-h')) {
    showHelp();
    process.exit(0);
  }

  try {
    await applyStatisticsTemplateToDatabase(options);
    console.log('\n✨ 统计模板应用完成');
    process.exit(0);
  } catch (error) {
    console.error('\n💥 应用失败:', error);
    process.exit(1);
  }
}

// 执行主函数
main();

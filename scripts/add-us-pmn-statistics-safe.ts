#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

async function addUsPmnStatisticsSafe() {
  console.log('🚀 安全地为 us_pmn 启用统计功能 (只新增/更新，不删除)...');
  
  try {
    // 1. 查看当前 us_pmn 的统计配置状态
    console.log('\n📊 1. 查看当前 us_pmn 统计配置状态...');
    const currentStats = await db.fieldConfig.findMany({
      where: {
        databaseCode: 'us_pmn',
        isStatisticsEnabled: true,
        isActive: true
      }
    });
    
    console.log(`当前已启用统计的字段数: ${currentStats.length}`);
    if (currentStats.length > 0) {
      console.log('已启用统计的字段:');
      currentStats.forEach(field => {
        console.log(`  - ${field.fieldName}: ${field.statisticsDisplayName}`);
      });
    }
    
    // 2. 参考 us_class 的统计配置
    console.log('\n📋 2. 参考 us_class 的统计配置...');
    const usClassStats = await db.fieldConfig.findMany({
      where: {
        databaseCode: 'us_class',
        isStatisticsEnabled: true,
        isActive: true
      },
      select: {
        fieldName: true,
        statisticsDisplayName: true,
        statisticsDefaultLimit: true,
        statisticsMaxLimit: true
      }
    });
    
    console.log('us_class 的统计配置:');
    usClassStats.forEach(stat => {
      console.log(`  - ${stat.fieldName}: ${stat.statisticsDisplayName} (默认:${stat.statisticsDefaultLimit}, 最大:${stat.statisticsMaxLimit})`);
    });
    
    // 3. 为 us_pmn 的现有字段启用统计功能 (只更新，不新增字段)
    console.log('\n🔧 3. 为 us_pmn 现有字段启用统计功能...');
    
    const statisticsUpdates = [
      {
        fieldName: 'decision',
        statisticsDisplayName: '审批决定分布',
        statisticsOrder: 1,
        statisticsDefaultLimit: 5,
        statisticsMaxLimit: 15
      },
      {
        fieldName: 'productcode',
        statisticsDisplayName: '产品代码统计',
        statisticsOrder: 2,
        statisticsDefaultLimit: 8,
        statisticsMaxLimit: 100
      },
      {
        fieldName: 'applicant',
        statisticsDisplayName: '申请人统计',
        statisticsOrder: 3,
        statisticsDefaultLimit: 10,
        statisticsMaxLimit: 50
      },
      {
        fieldName: 'country_code',
        statisticsDisplayName: '国家分布',
        statisticsOrder: 4,
        statisticsDefaultLimit: 10,
        statisticsMaxLimit: 30
      },
      {
        fieldName: 'type',
        statisticsDisplayName: '类型分布',
        statisticsOrder: 5,
        statisticsDefaultLimit: 5,
        statisticsMaxLimit: 20
      }
    ];
    
    for (const update of statisticsUpdates) {
      try {
        // 检查字段是否存在
        const existingField = await db.fieldConfig.findFirst({
          where: {
            databaseCode: 'us_pmn',
            fieldName: update.fieldName,
            isActive: true
          }
        });
        
        if (existingField) {
          // 只更新现有字段，启用统计功能
          const result = await db.fieldConfig.updateMany({
            where: {
              databaseCode: 'us_pmn',
              fieldName: update.fieldName,
              isActive: true
            },
            data: {
              isStatisticsEnabled: true,
              statisticsOrder: update.statisticsOrder,
              statisticsDisplayName: update.statisticsDisplayName,
              statisticsDefaultLimit: update.statisticsDefaultLimit,
              statisticsMaxLimit: update.statisticsMaxLimit,
              statisticsSortOrder: 'desc',
              updatedAt: new Date()
            }
          });
          
          if (result.count > 0) {
            console.log(`  ✅ ${update.fieldName}: 已启用统计功能`);
            console.log(`     显示名: ${update.statisticsDisplayName}`);
            console.log(`     默认显示: ${update.statisticsDefaultLimit}, 最大: ${update.statisticsMaxLimit}`);
          } else {
            console.log(`  ⚠️  ${update.fieldName}: 更新失败`);
          }
        } else {
          console.log(`  ⚠️  ${update.fieldName}: 字段不存在，跳过`);
        }
      } catch (error) {
        console.error(`  ❌ ${update.fieldName}: 更新出错 -`, error.message);
      }
    }
    
    // 4. 验证更新结果
    console.log('\n🔍 4. 验证更新结果...');
    const updatedStats = await db.fieldConfig.findMany({
      where: {
        databaseCode: 'us_pmn',
        isStatisticsEnabled: true,
        isActive: true
      },
      select: {
        fieldName: true,
        statisticsDisplayName: true,
        statisticsOrder: true,
        statisticsDefaultLimit: true,
        statisticsMaxLimit: true
      },
      orderBy: { statisticsOrder: 'asc' }
    });
    
    console.log(`\n✅ us_pmn 现在有 ${updatedStats.length} 个统计字段:`);
    updatedStats.forEach((field, index) => {
      console.log(`  ${index + 1}. ${field.statisticsDisplayName} (${field.fieldName})`);
      console.log(`     默认显示: ${field.statisticsDefaultLimit}, 最大: ${field.statisticsMaxLimit}`);
    });
    
    // 5. 功能对比
    console.log('\n📊 5. 更新后功能对比...');
    
    const usPmnFieldCount = await db.fieldConfig.count({
      where: { databaseCode: 'us_pmn', isActive: true }
    });
    
    const usPmnStatsCount = await db.fieldConfig.count({
      where: { databaseCode: 'us_pmn', isStatisticsEnabled: true, isActive: true }
    });
    
    const usClassStatsCount = await db.fieldConfig.count({
      where: { databaseCode: 'us_class', isStatisticsEnabled: true, isActive: true }
    });
    
    console.log('  功能对比:');
    console.log(`    us_pmn 总字段数: ${usPmnFieldCount}`);
    console.log(`    us_pmn 统计字段: ${usPmnStatsCount}`);
    console.log(`    us_class 统计字段: ${usClassStatsCount}`);
    
    if (usPmnStatsCount >= usClassStatsCount) {
      console.log('  ✅ us_pmn 统计功能已达到 us_class 水平！');
    } else {
      console.log(`  ⚠️  us_pmn 统计功能还需要 ${usClassStatsCount - usPmnStatsCount} 个字段`);
    }
    
    console.log('\n🎉 安全更新完成！');
    console.log('\n💡 现在 us_pmn 具备完整的统计功能:');
    console.log('  ✅ 审批决定分布图表');
    console.log('  ✅ 产品代码统计');
    console.log('  ✅ 申请人统计');
    console.log('  ✅ 国家分布图表');
    console.log('  ✅ 类型分布统计');
    
    console.log('\n🔗 查看效果:');
    console.log('  访问: http://localhost:3001/data/list/us_pmn');
    console.log('  在页面右侧查看统计图表');
    
  } catch (error) {
    console.error('❌ 安全更新失败:', error);
  } finally {
    await db.$disconnect();
  }
}

enableUsPmnStatisticsSafe().catch(console.error);

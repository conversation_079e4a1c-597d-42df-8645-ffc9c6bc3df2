#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 企业级权限系统初始化脚本
 * 
 * 功能：
 * 1. 创建系统预设角色
 * 2. 创建权限配置
 * 3. 设置默认管理员账户
 * 4. 初始化数据库配置
 * 5. 创建权限关联关系
 */

// 系统预设角色
const SYSTEM_ROLES = [
  {
    name: 'admin',
    displayName: '系统管理员',
    description: '拥有所有权限的系统管理员',
    priority: 1000,
    isSystem: true,
  },
  {
    name: 'enterprise_user',
    displayName: '企业用户',
    description: '企业级会员，拥有完整数据访问权限',
    priority: 300,
    isSystem: true,
  },
  {
    name: 'premium_user',
    displayName: '高级用户',
    description: '高级会员，拥有大部分数据访问权限',
    priority: 200,
    isSystem: true,
  },
  {
    name: 'free_user',
    displayName: '免费用户',
    description: '免费用户，拥有基础数据访问权限',
    priority: 100,
    isSystem: true,
  },
];

// 权限配置
const PERMISSIONS = [
  // 数据库权限
  { name: 'database:us_class:read', displayName: '美国分类数据-读取', category: 'database', resource: 'us_class', action: 'read' },
  { name: 'database:us_pmn:read', displayName: '美国PMN数据-读取', category: 'database', resource: 'us_pmn', action: 'read' },
  { name: 'database:us_pmn:export', displayName: '美国PMN数据-导出', category: 'database', resource: 'us_pmn', action: 'export' },
  { name: 'database:devicecnimported:read', displayName: '中国器械数据-读取', category: 'database', resource: 'devicecnimported', action: 'read' },
  { name: 'database:devicecnimported:export', displayName: '中国器械数据-导出', category: 'database', resource: 'devicecnimported', action: 'export' },
  { name: 'database:freepat:read', displayName: '免费专利数据-读取', category: 'database', resource: 'freepat', action: 'read' },
  
  // 功能权限
  { name: 'feature:advanced_search', displayName: '高级搜索', category: 'feature', resource: 'advanced_search', action: 'use' },
  { name: 'feature:data_export', displayName: '数据导出', category: 'feature', resource: 'data_export', action: 'use' },
  { name: 'feature:analytics', displayName: '数据分析', category: 'feature', resource: 'analytics', action: 'use' },
  { name: 'feature:bulk_download', displayName: '批量下载', category: 'feature', resource: 'bulk_download', action: 'use' },
  
  // 管理权限
  { name: 'admin:user_management', displayName: '用户管理', category: 'admin', resource: 'user_management', action: 'full' },
  { name: 'admin:permission_management', displayName: '权限管理', category: 'admin', resource: 'permission_management', action: 'full' },
  { name: 'admin:system_settings', displayName: '系统设置', category: 'admin', resource: 'system_settings', action: 'full' },
  { name: 'admin:audit_logs', displayName: '审计日志', category: 'admin', resource: 'audit_logs', action: 'read' },
];

// 角色-权限映射
const ROLE_PERMISSIONS = {
  admin: [
    // 管理员拥有所有权限
    ...PERMISSIONS.map(p => p.name),
  ],
  
  enterprise_user: [
    // 企业用户拥有所有数据库和功能权限
    'database:us_class:read',
    'database:us_pmn:read',
    'database:us_pmn:export',
    'database:devicecnimported:read',
    'database:devicecnimported:export',
    'database:freepat:read',
    'feature:advanced_search',
    'feature:data_export',
    'feature:analytics',
    'feature:bulk_download',
  ],
  
  premium_user: [
    // 高级用户拥有主要数据库权限和部分功能
    'database:us_class:read',
    'database:us_pmn:read',
    'database:devicecnimported:read',
    'database:devicecnimported:export',
    'database:freepat:read',
    'feature:advanced_search',
    'feature:data_export',
  ],
  
  free_user: [
    // 免费用户只能访问免费数据库
    'database:us_class:read',
    'database:freepat:read',
    'database:devicecnimported:read',
  ],
};

// 数据库配置
const DATABASE_CONFIGS = [
  {
    code: 'us_class',
    name: '美国器械分类',
    category: '参考数据',
    description: 'FDA医疗器械分类目录',
    defaultAccessLevel: 'FREE' as const,
    requiresAuth: false,
    tableName: 'us_class',
    modelName: 'uSClass',
    quotaConfig: {
      dailyQueryLimit: -1,
      exportLimit: 0,
    },
  },
  {
    code: 'us_pmn',
    name: '美国PMN数据',
    category: '审批数据',
    description: '美国FDA 510(k)申请数据',
    defaultAccessLevel: 'PREMIUM' as const,
    requiresAuth: true,
    tableName: 'us_pmn',
    modelName: 'usPmn',
    quotaConfig: {
      dailyQueryLimit: 1000,
      exportLimit: 500,
    },
  },
  {
    code: 'devicecnimported',
    name: '中国器械数据',
    category: '审批数据',
    description: '中国NMPA医疗器械审批数据',
    defaultAccessLevel: 'FREE' as const,
    requiresAuth: false,
    tableName: 'medical_device',
    modelName: 'medicalDevice',
    quotaConfig: {
      dailyQueryLimit: 500,
      exportLimit: 100,
    },
  },
  {
    code: 'freepat',
    name: '免费专利数据',
    category: '专利数据',
    description: '公开的医疗器械专利信息',
    defaultAccessLevel: 'FREE' as const,
    requiresAuth: false,
    tableName: 'free_patent',
    modelName: 'freePatent',
    quotaConfig: {
      dailyQueryLimit: -1,
      exportLimit: 0,
    },
  },
];

/**
 * 主初始化函数
 */
async function initializeEnterpriseSystem() {
  console.log('🚀 开始初始化企业级权限系统...\n');
  
  try {
    // 1. 创建系统角色
    await createSystemRoles();
    
    // 2. 创建权限配置
    await createPermissions();
    
    // 3. 创建角色-权限关联
    await createRolePermissions();
    
    // 4. 创建/更新数据库配置
    await updateDatabaseConfigs();
    
    // 5. 创建默认管理员（如果不存在）
    await createDefaultAdmin();
    
    // 6. 验证系统完整性
    await verifySystemIntegrity();
    
    console.log('\n🎉 企业级权限系统初始化完成！');
    console.log('📋 系统状态：');
    console.log(`  - 角色数量: ${SYSTEM_ROLES.length}`);
    console.log(`  - 权限数量: ${PERMISSIONS.length}`);
    console.log(`  - 数据库配置: ${DATABASE_CONFIGS.length}`);
    console.log('\n💡 下一步建议：');
    console.log('  1. 配置Redis缓存服务');
    console.log('  2. 更新应用环境变量');
    console.log('  3. 部署新的中间件系统');
    console.log('  4. 进行权限系统测试');
    
  } catch (error) {
    console.error('❌ 初始化过程中出现错误：', error);
    throw error;
  } finally {
    await db.$disconnect();
  }
}

/**
 * 创建系统角色
 */
async function createSystemRoles() {
  console.log('📋 创建系统角色...');
  
  for (const roleData of SYSTEM_ROLES) {
    const role = await db.role.upsert({
      where: { name: roleData.name },
      update: {
        displayName: roleData.displayName,
        description: roleData.description,
        priority: roleData.priority,
        isSystem: roleData.isSystem,
        isActive: true,
      },
      create: roleData,
    });
    
    console.log(`  ✅ 角色创建/更新: ${role.displayName} (${role.name})`);
  }
}

/**
 * 创建权限配置
 */
async function createPermissions() {
  console.log('\n🔐 创建权限配置...');
  
  for (const permData of PERMISSIONS) {
    const permission = await db.permission.upsert({
      where: { name: permData.name },
      update: {
        displayName: permData.displayName,
        category: permData.category,
        resource: permData.resource,
        action: permData.action,
        isSystem: true,
        isActive: true,
      },
      create: {
        ...permData,
        isSystem: true,
        isActive: true,
      },
    });
    
    console.log(`  ✅ 权限创建/更新: ${permission.displayName} (${permission.name})`);
  }
}

/**
 * 创建角色-权限关联
 */
async function createRolePermissions() {
  console.log('\n🔗 创建角色-权限关联...');
  
  for (const [roleName, permissionNames] of Object.entries(ROLE_PERMISSIONS)) {
    const role = await db.role.findUnique({ where: { name: roleName } });
    if (!role) {
      console.log(`  ⚠️  角色 ${roleName} 不存在，跳过权限分配`);
      continue;
    }
    
    console.log(`  🔧 为角色 ${role.displayName} 分配权限...`);
    
    // 清除现有权限关联
    await db.rolePermission.deleteMany({
      where: { roleId: role.id },
    });
    
    // 创建新的权限关联
    for (const permissionName of permissionNames) {
      const permission = await db.permission.findUnique({ 
        where: { name: permissionName } 
      });
      
      if (!permission) {
        console.log(`    ⚠️  权限 ${permissionName} 不存在，跳过`);
        continue;
      }
      
      await db.rolePermission.create({
        data: {
          roleId: role.id,
          permissionId: permission.id,
          isActive: true,
        },
      });
      
      console.log(`    ✅ 已分配权限: ${permission.displayName}`);
    }
  }
}

/**
 * 更新数据库配置
 */
async function updateDatabaseConfigs() {
  console.log('\n💾 更新数据库配置...');
  
  for (const configData of DATABASE_CONFIGS) {
    const config = await db.databaseConfig.upsert({
      where: { code: configData.code },
      update: {
        name: configData.name,
        category: configData.category,
        description: configData.description,
        defaultAccessLevel: configData.defaultAccessLevel,
        requiresAuth: configData.requiresAuth,
        tableName: configData.tableName,
        modelName: configData.modelName,
        quotaConfig: configData.quotaConfig,
        isActive: true,
      },
      create: {
        ...configData,
        isActive: true,
      },
    });
    
    console.log(`  ✅ 数据库配置: ${config.name} (${config.code})`);
  }
}

/**
 * 创建默认管理员
 */
async function createDefaultAdmin() {
  console.log('\n👤 检查默认管理员账户...');
  
  const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
  const adminPassword = process.env.ADMIN_PASSWORD || 'Admin@123456';
  
  // 检查是否已存在管理员
  const existingAdmin = await db.user.findFirst({
    where: {
      OR: [
        { email: adminEmail },
        { membershipType: 'ADMIN' },
      ],
    },
  });
  
  if (existingAdmin) {
    console.log(`  ℹ️  管理员账户已存在: ${existingAdmin.email}`);
    return;
  }
  
  // 创建管理员账户
  const bcrypt = require('bcrypt');
  const hashedPassword = await bcrypt.hash(adminPassword, 10);
  
  const admin = await db.user.create({
    data: {
      email: adminEmail,
      password: hashedPassword,
      name: '系统管理员',
      membershipType: 'ADMIN',
      isActive: true,
      emailVerified: true,
    },
  });
  
  // 分配管理员角色
  const adminRole = await db.role.findUnique({ where: { name: 'admin' } });
  if (adminRole) {
    await db.userRole.create({
      data: {
        userId: admin.id,
        roleId: adminRole.id,
        isActive: true,
      },
    });
  }
  
  console.log(`  ✅ 默认管理员已创建:`);
  console.log(`     邮箱: ${adminEmail}`);
  console.log(`     密码: ${adminPassword}`);
  console.log(`     ⚠️  请登录后立即修改密码！`);
}

/**
 * 验证系统完整性
 */
async function verifySystemIntegrity() {
  console.log('\n🔍 验证系统完整性...');
  
  // 检查角色数量
  const roleCount = await db.role.count({ where: { isActive: true } });
  console.log(`  📊 活跃角色数量: ${roleCount}`);
  
  // 检查权限数量
  const permissionCount = await db.permission.count({ where: { isActive: true } });
  console.log(`  📊 活跃权限数量: ${permissionCount}`);
  
  // 检查数据库配置
  const dbConfigCount = await db.databaseConfig.count({ where: { isActive: true } });
  console.log(`  📊 数据库配置数量: ${dbConfigCount}`);
  
  // 检查权限关联
  const rolePermissionCount = await db.rolePermission.count({ where: { isActive: true } });
  console.log(`  📊 角色-权限关联数量: ${rolePermissionCount}`);
  
  // 检查管理员账户
  const adminCount = await db.user.count({ 
    where: { 
      membershipType: 'ADMIN',
      isActive: true,
    }
  });
  console.log(`  📊 管理员账户数量: ${adminCount}`);
  
  if (roleCount === 0 || permissionCount === 0 || dbConfigCount === 0) {
    throw new Error('系统完整性验证失败：关键数据缺失');
  }
  
  console.log('  ✅ 系统完整性验证通过');
}

/**
 * 清理和重置系统（谨慎使用）
 */
async function resetSystem() {
  console.log('⚠️  开始重置权限系统...');
  
  // 删除所有权限关联
  await db.rolePermission.deleteMany({});
  await db.userPermission.deleteMany({});
  await db.userRole.deleteMany({});
  
  // 删除权限和角色
  await db.permission.deleteMany({ where: { isSystem: true } });
  await db.role.deleteMany({ where: { isSystem: true } });
  
  console.log('✅ 系统重置完成');
}

// 执行初始化
if (require.main === module) {
  const command = process.argv[2];
  
  if (command === 'reset') {
    resetSystem()
      .then(() => {
        console.log('权限系统重置完成');
        process.exit(0);
      })
      .catch(error => {
        console.error('重置失败：', error);
        process.exit(1);
      });
  } else {
    initializeEnterpriseSystem()
      .then(() => {
        console.log('企业级权限系统初始化成功');
        process.exit(0);
      })
      .catch(error => {
        console.error('初始化失败：', error);
        process.exit(1);
      });
  }
}

export { initializeEnterpriseSystem, resetSystem }; 
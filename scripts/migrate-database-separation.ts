#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client';
import { getDynamicModel, DATABASE_TABLE_MAPPING, getAllDatabaseCodes } from '../src/lib/dynamicTableMapping';
import { generateBusinessKeyInfo } from '../src/lib/utils';
import { getTableConfig } from '../src/lib/uniqueKeyConfig';

const prisma = new PrismaClient();

interface MigrationResult {
  databaseCode: string;
  totalRecords: number;
  migratedRecords: number;
  skippedRecords: number;
  errorRecords: number;
  errors: Array<{
    id: string;
    error: string;
  }>;
}

interface LegacyMedicalDevice {
  id: string;
  productName: string;
  companyName: string;
  registrationNumber?: string;
  managementType?: string;
  approvalDate?: Date;
  validUntil?: Date;
  category?: string;
  structureOrUse?: string;
  productionAddress?: string;
  companyAddress?: string;
  specifications?: string;
  structure?: string;
  scope?: string;
  storageConditions?: string;
  accessories?: string;
  otherContent?: string;
  notes?: string;
  classification?: string;
  approvalDepartment?: string;
  changeHistory?: string;
  isInnovative?: boolean;
  isClinicalNeed?: boolean;
  isChildrenSpecific?: boolean;
  isRareDisease?: boolean;
  database: string; // 这是我们要移除的字段
  businessKey: string;
  businessKeyHash?: string;
  dataVersion: number;
  isActive: boolean;
  importedAt: Date;
  updatedAt: Date;
  createdAt: Date;
}

/**
 * 数据迁移主函数
 */
async function migrateToSeparateTables(): Promise<MigrationResult[]> {
  console.log('🚀 开始数据库分离迁移...');
  
  const results: MigrationResult[] = [];
  const supportedDatabases = getAllDatabaseCodes();
  
  console.log(`📋 支持的数据库: ${supportedDatabases.join(', ')}`);

  // 获取所有需要迁移的数据库类型
  const databaseTypes = await prisma.medicalDevice.groupBy({
    by: ['database'],
    _count: { database: true }
  });

  console.log('\n📊 当前数据分布:');
  databaseTypes.forEach(({ database, _count }) => {
    console.log(`  ${database}: ${_count.database} 条记录`);
  });

  // 为每个数据库类型执行迁移
  for (const { database: databaseCode } of databaseTypes) {
    if (!supportedDatabases.includes(databaseCode)) {
      console.log(`⚠️  跳过不支持的数据库: ${databaseCode}`);
      continue;
    }

    console.log(`\n🔄 开始迁移数据库: ${databaseCode}`);
    const result = await migrateSingleDatabase(databaseCode);
    results.push(result);
  }

  return results;
}

/**
 * 迁移单个数据库的数据
 */
async function migrateSingleDatabase(databaseCode: string): Promise<MigrationResult> {
  const result: MigrationResult = {
    databaseCode,
    totalRecords: 0,
    migratedRecords: 0,
    skippedRecords: 0,
    errorRecords: 0,
    errors: []
  };

  try {
    // 获取目标模型
    const targetModel = getDynamicModel(databaseCode);
    
    // 获取源数据
    const sourceRecords = await prisma.medicalDevice.findMany({
      where: { database: databaseCode }
    });

    result.totalRecords = sourceRecords.length;
    console.log(`  📥 找到 ${result.totalRecords} 条记录需要迁移`);

    if (result.totalRecords === 0) {
      console.log(`  ✅ 没有数据需要迁移`);
      return result;
    }

    // 获取表配置用于重新生成businessKey
    const tableConfig = getTableConfig('MedicalDevice');
    if (!tableConfig) {
      throw new Error('找不到MedicalDevice表配置');
    }

    // 批量迁移数据
    const batchSize = 100;
    for (let i = 0; i < sourceRecords.length; i += batchSize) {
      const batch = sourceRecords.slice(i, i + batchSize);
      console.log(`  🔄 处理批次 ${Math.floor(i / batchSize) + 1}/${Math.ceil(sourceRecords.length / batchSize)}`);
      
      for (const record of batch) {
        try {
          // 生成新的businessKey（不包含database字段）
          const newBusinessKeyInfo = generateNewBusinessKey(record, databaseCode, tableConfig);
          
          // 准备插入数据（移除database字段）
          const { database: _, ...recordWithoutDatabase } = record;
          const newRecord = {
            ...recordWithoutDatabase,
            businessKey: newBusinessKeyInfo.businessKey,
            businessKeyHash: newBusinessKeyInfo.businessKeyHash
          };

          // 检查是否已存在
          const existing = await targetModel.findUnique({
            where: { businessKey: newRecord.businessKey }
          });

          if (existing) {
            result.skippedRecords++;
            console.log(`    ⏭️  跳过已存在记录: ${newRecord.businessKey}`);
            continue;
          }

          // 插入到新表
          await targetModel.create({
            data: newRecord
          });
          
          result.migratedRecords++;
          
        } catch (error) {
          result.errorRecords++;
          result.errors.push({
            id: record.id,
            error: error instanceof Error ? error.message : String(error)
          });
          console.error(`    ❌ 迁移记录失败: ${record.id}`, error);
        }
      }
    }

    console.log(`  ✅ 迁移完成: ${result.migratedRecords}/${result.totalRecords} 成功`);
    
  } catch (error) {
    console.error(`❌ 迁移数据库 ${databaseCode} 时发生错误:`, error);
    result.errors.push({
      id: 'MIGRATION_ERROR',
      error: error instanceof Error ? error.message : String(error)
    });
  }

  return result;
}

/**
 * 生成新的businessKey（不包含database字段）
 */
function generateNewBusinessKey(record: LegacyMedicalDevice, databaseCode: string, tableConfig: any) {
  // 创建上下文对象
  const context = {
    databaseCode,
    tableName: 'MedicalDevice',
    importSource: 'MIGRATION'
  };

  // 使用新的uniqueKeyRule生成businessKey
  const businessKey = tableConfig.uniqueKeyRule(record, context);
  
  // 生成Hash（如果启用）
  if (tableConfig.hashConfig?.enabled) {
    const { businessKeyHash } = generateBusinessKeyInfo(
      record, 
      (row: any, ctx: any) => tableConfig.uniqueKeyRule(row, ctx),
      tableConfig.hashConfig.algorithm
    );
    return { businessKey, businessKeyHash };
  }
  
  return { businessKey, businessKeyHash: undefined };
}

/**
 * 验证迁移结果
 */
async function validateMigration(results: MigrationResult[]): Promise<boolean> {
  console.log('\n🔍 验证迁移结果...');
  
  let isValid = true;
  
  for (const result of results) {
    console.log(`\n📊 ${result.databaseCode} 迁移统计:`);
    console.log(`  总记录数: ${result.totalRecords}`);
    console.log(`  成功迁移: ${result.migratedRecords}`);
    console.log(`  跳过记录: ${result.skippedRecords}`);
    console.log(`  错误记录: ${result.errorRecords}`);
    
    if (result.errorRecords > 0) {
      isValid = false;
      console.log(`  ❌ 发现错误，前5个错误:`);
      result.errors.slice(0, 5).forEach(error => {
        console.log(`    - ${error.id}: ${error.error}`);
      });
    }
    
    // 验证数据完整性
    try {
      const targetModel = getDynamicModel(result.databaseCode);
      const targetCount = await targetModel.count();
      const expectedCount = result.migratedRecords;
      
      if (targetCount !== expectedCount) {
        console.log(`  ⚠️  数据计数不匹配: 预期 ${expectedCount}, 实际 ${targetCount}`);
        isValid = false;
      } else {
        console.log(`  ✅ 数据验证通过: ${targetCount} 条记录`);
      }
    } catch (error) {
      console.error(`  ❌ 验证失败:`, error);
      isValid = false;
    }
  }
  
  return isValid;
}

/**
 * 清理旧数据（谨慎操作）
 */
async function cleanupOldData(dryRun: boolean = true): Promise<void> {
  console.log(`\n🧹 ${dryRun ? '预览' : '执行'}数据清理...`);
  
  const totalRecords = await prisma.medicalDevice.count();
  console.log(`旧表中共有 ${totalRecords} 条记录`);
  
  if (dryRun) {
    console.log('这是预览模式，不会删除任何数据');
    console.log('要实际执行删除，请使用 --cleanup-force 参数');
    return;
  }
  
  console.log('⚠️  警告：即将删除旧表中的所有数据！');
  console.log('请确认您已经验证了迁移结果的正确性');
  
  // 这里可以添加额外的确认步骤
  // await prisma.medicalDevice.deleteMany({});
  console.log('为安全起见，请手动执行删除操作');
}

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2);
  const dryRun = !args.includes('--execute');
  const cleanup = args.includes('--cleanup');
  const cleanupForce = args.includes('--cleanup-force');
  
  try {
    if (dryRun) {
      console.log('🔍 这是预览模式，不会执行实际迁移');
      console.log('要执行实际迁移，请使用 --execute 参数\n');
    }
    
    const results = await migrateToSeparateTables();
    const isValid = await validateMigration(results);
    
    if (cleanup) {
      await cleanupOldData(!cleanupForce);
    }
    
    console.log('\n📋 迁移总结:');
    const totalMigrated = results.reduce((sum, r) => sum + r.migratedRecords, 0);
    const totalErrors = results.reduce((sum, r) => sum + r.errorRecords, 0);
    
    console.log(`总计迁移: ${totalMigrated} 条记录`);
    console.log(`总计错误: ${totalErrors} 条记录`);
    console.log(`迁移状态: ${isValid ? '✅ 成功' : '❌ 有错误'}`);
    
    if (!dryRun && isValid) {
      console.log('\n🎉 数据库分离迁移完成！');
      console.log('现在可以：');
      console.log('1. 运行 npm run prisma:generate 重新生成Prisma客户端');
      console.log('2. 更新应用代码使用新的动态表映射系统');
      console.log('3. 测试所有功能确保正常工作');
      console.log('4. 清理旧的MedicalDevice表（可选）');
    }
    
  } catch (error) {
    console.error('❌ 迁移过程中发生错误:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
} 
#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

async function enableUsPmnStatistics() {
  console.log('🚀 为 us_pmn 启用统计功能，复用 us_class 的统计配置模式...');
  
  try {
    // 1. 参考 us_class 的统计配置
    console.log('\n📊 1. 查看 us_class 的统计配置模式...');
    const usClassStats = await db.fieldConfig.findMany({
      where: {
        databaseCode: 'us_class',
        isStatisticsEnabled: true,
        isActive: true
      },
      select: {
        fieldName: true,
        statisticsDisplayName: true,
        statisticsOrder: true,
        statisticsDefaultLimit: true,
        statisticsMaxLimit: true,
        statisticsSortOrder: true
      },
      orderBy: { statisticsOrder: 'asc' }
    });
    
    console.log('✅ us_class 统计配置:');
    usClassStats.forEach((stat, index) => {
      console.log(`  ${index + 1}. ${stat.statisticsDisplayName} (${stat.fieldName})`);
      console.log(`     默认显示: ${stat.statisticsDefaultLimit}, 最大: ${stat.statisticsMaxLimit}`);
    });
    
    // 2. 为 us_pmn 配置统计功能
    console.log('\n🔧 2. 为 us_pmn 配置统计功能...');
    
    const usPmnStatsConfig = [
      {
        fieldName: 'decision',
        statisticsDisplayName: '审批决定分布',
        statisticsOrder: 1,
        statisticsDefaultLimit: 5,
        statisticsMaxLimit: 15,
        description: '显示不同审批决定的数量分布'
      },
      {
        fieldName: 'productcode',
        statisticsDisplayName: '产品代码统计',
        statisticsOrder: 2,
        statisticsDefaultLimit: 8,
        statisticsMaxLimit: 100,
        description: '显示最常见的产品代码'
      },
      {
        fieldName: 'applicant',
        statisticsDisplayName: '申请人统计',
        statisticsOrder: 3,
        statisticsDefaultLimit: 10,
        statisticsMaxLimit: 50,
        description: '显示最活跃的申请人'
      },
      {
        fieldName: 'country_code',
        statisticsDisplayName: '国家分布',
        statisticsOrder: 4,
        statisticsDefaultLimit: 10,
        statisticsMaxLimit: 30,
        description: '显示申请人国家分布'
      },
      {
        fieldName: 'type',
        statisticsDisplayName: '类型分布',
        statisticsOrder: 5,
        statisticsDefaultLimit: 5,
        statisticsMaxLimit: 20,
        description: '显示不同类型的分布'
      }
    ];
    
    console.log(`🔧 配置 ${usPmnStatsConfig.length} 个统计字段...`);
    
    for (const config of usPmnStatsConfig) {
      try {
        const result = await db.fieldConfig.updateMany({
          where: {
            databaseCode: 'us_pmn',
            fieldName: config.fieldName,
            isActive: true,
          },
          data: {
            isStatisticsEnabled: true,
            statisticsOrder: config.statisticsOrder,
            statisticsDisplayName: config.statisticsDisplayName,
            statisticsDefaultLimit: config.statisticsDefaultLimit,
            statisticsMaxLimit: config.statisticsMaxLimit,
            statisticsSortOrder: 'desc', // 按数量降序排列
            updatedAt: new Date(),
          },
        });

        if (result.count > 0) {
          console.log(`  ✅ ${config.fieldName}: ${config.statisticsDisplayName}`);
          console.log(`     ${config.description}`);
        } else {
          console.log(`  ⚠️  ${config.fieldName} - 未找到匹配的字段配置`);
        }
      } catch (error) {
        console.error(`  ❌ ${config.fieldName} - 更新失败:`, error);
      }
    }
    
    // 3. 验证统计配置
    console.log('\n🔍 3. 验证统计配置...');
    const updatedStats = await db.fieldConfig.findMany({
      where: {
        databaseCode: 'us_pmn',
        isStatisticsEnabled: true,
        isActive: true,
      },
      select: {
        fieldName: true,
        statisticsDisplayName: true,
        statisticsOrder: true,
        statisticsDefaultLimit: true,
        statisticsMaxLimit: true,
      },
      orderBy: {
        statisticsOrder: 'asc'
      }
    });

    console.log('\n📋 us_pmn 已配置的统计功能:');
    updatedStats.forEach((field, index) => {
      console.log(`\n   ${index + 1}. ${field.statisticsDisplayName} (${field.fieldName})`);
      console.log(`      默认显示: ${field.statisticsDefaultLimit} 项`);
      console.log(`      最大显示: ${field.statisticsMaxLimit} 项`);
    });
    
    // 4. 测试统计 API
    console.log('\n🌐 4. 测试统计 API...');
    try {
      const response = await fetch('http://localhost:3001/api/stats/us_pmn/configurable');
      const result = await response.json();
      
      if (result.success) {
        console.log('✅ 可配置统计 API 测试成功');
        console.log(`   返回统计项数量: ${result.data?.length || 0}`);
        if (result.data && result.data.length > 0) {
          console.log('   统计项预览:');
          result.data.slice(0, 2).forEach((stat: any, index: number) => {
            console.log(`     ${index + 1}. ${stat.title}: ${stat.data?.length || 0} 项`);
          });
        }
      } else {
        console.log('❌ 可配置统计 API 测试失败:', result.error);
      }
    } catch (error) {
      console.log('❌ 统计 API 请求失败:', error.message);
    }
    
    // 5. 对比更新后的功能
    console.log('\n📊 5. 功能对比 (更新后)...');
    
    const usPmnFields = await db.fieldConfig.findMany({
      where: { databaseCode: 'us_pmn', isActive: true }
    });
    
    const usClassFields = await db.fieldConfig.findMany({
      where: { databaseCode: 'us_class', isActive: true }
    });
    
    console.log('\n  更新后功能对比:');
    console.log('  ┌─────────────────────┬─────────┬──────────┐');
    console.log('  │ 功能                │ us_pmn  │ us_class │');
    console.log('  ├─────────────────────┼─────────┼──────────┤');
    console.log(`  │ 启用统计字段        │ ${usPmnFields.filter(f => f.isStatisticsEnabled).length.toString().padEnd(7)} │ ${usClassFields.filter(f => f.isStatisticsEnabled).length.toString().padEnd(8)} │`);
    console.log(`  │ 可筛选字段          │ ${usPmnFields.filter(f => f.isFilterable).length.toString().padEnd(7)} │ ${usClassFields.filter(f => f.isFilterable).length.toString().padEnd(8)} │`);
    console.log(`  │ Multi-select 筛选   │ ${usPmnFields.filter(f => f.filterType === 'multi_select').length.toString().padEnd(7)} │ ${usClassFields.filter(f => f.filterType === 'multi_select').length.toString().padEnd(8)} │`);
    console.log('  └─────────────────────┴─────────┴──────────┘');
    
    console.log('\n🎉 us_pmn 统计功能配置完成！');
    console.log('\n💡 现在 us_pmn 具备与 us_class 相同的功能:');
    console.log('  ✅ 数据列表显示');
    console.log('  ✅ 多种筛选类型 (包括 multi_select)');
    console.log('  ✅ 全文搜索功能');
    console.log('  ✅ 数据统计图表');
    console.log('  ✅ 数据导出功能');
    console.log('  ✅ 权限控制');
    
    console.log('\n🔗 访问链接:');
    console.log('  - 数据列表: http://localhost:3001/data/list/us_pmn');
    console.log('  - 统计页面: 在数据列表页面右侧查看统计图表');
    
  } catch (error) {
    console.error('❌ 配置统计功能失败:', error);
  } finally {
    await db.$disconnect();
  }
}

enableUsPmnStatistics().catch(console.error);

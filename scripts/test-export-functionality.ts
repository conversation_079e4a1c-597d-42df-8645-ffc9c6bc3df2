#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';
import { getDatabaseConfig } from '../src/lib/configCache';

/**
 * 测试导出功能配置
 */

async function testExportFunctionality() {
  console.log('🧪 测试导出功能配置...\n');

  try {
    // 1. 测试us_pmn导出字段配置
    console.log('📋 测试us_pmn导出字段配置...');
    
    const usPmnConfig = await getDatabaseConfig('us_pmn');
    if (!usPmnConfig) {
      console.log('  ❌ 未找到us_pmn配置');
      return;
    }

    const usPmnExportableFields = usPmnConfig.fields
      ?.filter(f => f.isActive !== false && f.isExportable !== false)
      ?.sort((a, b) => (a.exportOrder || a.listOrder || 0) - (b.exportOrder || b.listOrder || 0)) || [];

    console.log(`  📊 可导出字段数量: ${usPmnExportableFields.length}`);
    console.log('  📋 导出字段列表 (按导出顺序):');
    usPmnExportableFields.slice(0, 10).forEach((field, index) => {
      console.log(`    ${index + 1}. ${field.fieldName} (${field.exportDisplayName || field.displayName}) - 顺序: ${field.exportOrder || 0}`);
    });
    if (usPmnExportableFields.length > 10) {
      console.log(`    ... 还有 ${usPmnExportableFields.length - 10} 个字段`);
    }

    // 2. 测试us_class导出字段配置
    console.log('\n📋 测试us_class导出字段配置...');
    
    const usClassConfig = await getDatabaseConfig('us_class');
    if (!usClassConfig) {
      console.log('  ❌ 未找到us_class配置');
      return;
    }

    const usClassExportableFields = usClassConfig.fields
      ?.filter(f => f.isActive !== false && f.isExportable !== false)
      ?.sort((a, b) => (a.exportOrder || a.listOrder || 0) - (b.exportOrder || b.listOrder || 0)) || [];

    console.log(`  📊 可导出字段数量: ${usClassExportableFields.length}`);
    console.log('  📋 导出字段列表 (按导出顺序):');
    usClassExportableFields.forEach((field, index) => {
      console.log(`    ${index + 1}. ${field.fieldName} (${field.exportDisplayName || field.displayName}) - 顺序: ${field.exportOrder || 0}`);
    });

    // 3. 对比显示字段和导出字段的差异
    console.log('\n🔍 对比显示字段和导出字段...');
    
    // us_pmn对比
    const usPmnVisibleFields = usPmnConfig.fields?.filter(f => f.isActive !== false && f.isVisible) || [];
    console.log(`  us_pmn - 显示字段: ${usPmnVisibleFields.length}, 导出字段: ${usPmnExportableFields.length}`);
    
    const usPmnOnlyExport = usPmnExportableFields.filter(ef => 
      !usPmnVisibleFields.some(vf => vf.fieldName === ef.fieldName)
    );
    if (usPmnOnlyExport.length > 0) {
      console.log(`    仅导出不显示的字段 (${usPmnOnlyExport.length}个):`);
      usPmnOnlyExport.slice(0, 5).forEach(field => {
        console.log(`      - ${field.fieldName} (${field.exportDisplayName || field.displayName})`);
      });
      if (usPmnOnlyExport.length > 5) {
        console.log(`      ... 还有 ${usPmnOnlyExport.length - 5} 个`);
      }
    }

    // us_class对比
    const usClassVisibleFields = usClassConfig.fields?.filter(f => f.isActive !== false && f.isVisible) || [];
    console.log(`  us_class - 显示字段: ${usClassVisibleFields.length}, 导出字段: ${usClassExportableFields.length}`);
    
    const usClassOnlyExport = usClassExportableFields.filter(ef => 
      !usClassVisibleFields.some(vf => vf.fieldName === ef.fieldName)
    );
    if (usClassOnlyExport.length > 0) {
      console.log(`    仅导出不显示的字段 (${usClassOnlyExport.length}个):`);
      usClassOnlyExport.forEach(field => {
        console.log(`      - ${field.fieldName} (${field.exportDisplayName || field.displayName})`);
      });
    }

    // 4. 检查不可导出的字段
    console.log('\n❌ 不可导出的字段:');
    
    const usPmnNonExportable = usPmnConfig.fields?.filter(f => f.isActive !== false && f.isExportable === false) || [];
    if (usPmnNonExportable.length > 0) {
      console.log(`  us_pmn (${usPmnNonExportable.length}个):`);
      usPmnNonExportable.forEach(field => {
        console.log(`    - ${field.fieldName} (${field.displayName})`);
      });
    }

    const usClassNonExportable = usClassConfig.fields?.filter(f => f.isActive !== false && f.isExportable === false) || [];
    if (usClassNonExportable.length > 0) {
      console.log(`  us_class (${usClassNonExportable.length}个):`);
      usClassNonExportable.forEach(field => {
        console.log(`    - ${field.fieldName} (${field.displayName})`);
      });
    }

    console.log('\n✅ 导出功能测试完成！');
    console.log('\n📋 总结:');
    console.log(`  - us_pmn: ${usPmnExportableFields.length} 个可导出字段 (${usPmnVisibleFields.length} 个显示字段)`);
    console.log(`  - us_class: ${usClassExportableFields.length} 个可导出字段 (${usClassVisibleFields.length} 个显示字段)`);
    console.log('  - 导出字段已按 exportOrder 排序');
    console.log('  - 支持自定义导出列名 (exportDisplayName)');
    console.log('  - 系统字段已设为不导出');

    console.log('\n🚀 下一步:');
    console.log('  1. 测试导出API: GET /api/export/us_pmn?format=excel');
    console.log('  2. 测试导出API: GET /api/export/us_class?format=csv');
    console.log('  3. 根据需要调整字段的导出配置');

  } catch (error) {
    console.error('❌ 测试过程中出错:', error);
  } finally {
    await db.$disconnect();
  }
}

// 运行测试
if (require.main === module) {
  testExportFunctionality();
}

import { PrismaClient } from '@prisma/client';

const db = new PrismaClient();

async function performanceTest() {
  console.log('⚡ 翻页配置性能测试...\n');

  try {
    // 1. 测试当前实现的查询开销
    console.log('📊 测试当前实现的查询开销...');
    
    const iterations = 100;
    const startTime = performance.now();
    
    for (let i = 0; i < iterations; i++) {
      await db.databaseConfig.findUnique({
        where: { code: 'us_class' },
        select: { 
          defaultPageSize: true,
          maxPageSize: true,
          maxPages: true
        }
      });
    }
    
    const endTime = performance.now();
    const avgTime = (endTime - startTime) / iterations;
    
    console.log(`✅ ${iterations}次配置查询完成:`);
    console.log(`   总时间: ${(endTime - startTime).toFixed(2)}ms`);
    console.log(`   平均时间: ${avgTime.toFixed(2)}ms/次`);
    console.log(`   QPS: ${(1000 / avgTime).toFixed(0)} 查询/秒`);

    // 2. 模拟全局配置的性能
    console.log('\n🚀 测试全局配置的性能...');
    
    const GLOBAL_CONFIG = {
      DEFAULT_PAGE_SIZE: 20,
      MAX_PAGE_SIZE: 100,
      MAX_PAGES: 50,
    };
    
    const globalStartTime = performance.now();
    
    for (let i = 0; i < iterations; i++) {
      // 模拟全局配置访问（直接读取常量）
      const config = GLOBAL_CONFIG;
      const page = Math.max(1, Math.min(10, config.MAX_PAGES));
      const limit = Math.min(25, config.MAX_PAGE_SIZE);
    }
    
    const globalEndTime = performance.now();
    const globalAvgTime = (globalEndTime - globalStartTime) / iterations;
    
    console.log(`✅ ${iterations}次全局配置访问完成:`);
    console.log(`   总时间: ${(globalEndTime - globalStartTime).toFixed(2)}ms`);
    console.log(`   平均时间: ${globalAvgTime.toFixed(4)}ms/次`);
    console.log(`   QPS: ${(1000 / globalAvgTime).toFixed(0)} 操作/秒`);

    // 3. 性能对比
    console.log('\n📈 性能对比分析:');
    const speedup = avgTime / globalAvgTime;
    console.log(`   数据库查询: ${avgTime.toFixed(2)}ms/次`);
    console.log(`   全局配置: ${globalAvgTime.toFixed(4)}ms/次`);
    console.log(`   性能提升: ${speedup.toFixed(0)}x 倍`);
    console.log(`   节省时间: ${(avgTime - globalAvgTime).toFixed(2)}ms/次`);

    // 4. 实际场景影响评估
    console.log('\n🌐 实际场景影响评估:');
    
    const dailyRequests = 10000; // 假设每日10000次API请求
    const dailySavings = (avgTime - globalAvgTime) * dailyRequests;
    
    console.log(`   假设每日API请求: ${dailyRequests.toLocaleString()}次`);
    console.log(`   每日节省时间: ${dailySavings.toFixed(0)}ms = ${(dailySavings/1000).toFixed(1)}秒`);
    console.log(`   每月节省时间: ${(dailySavings*30/1000).toFixed(1)}秒`);

    // 5. 缓存效果测试
    console.log('\n💾 缓存效果测试...');
    
    // 第一次查询（冷缓存）
    const coldStart = performance.now();
    await db.databaseConfig.findUnique({
      where: { code: 'us_class' },
      select: { defaultPageSize: true, maxPageSize: true, maxPages: true }
    });
    const coldEnd = performance.now();
    
    // 连续查询（热缓存）
    const hotStart = performance.now();
    for (let i = 0; i < 10; i++) {
      await db.databaseConfig.findUnique({
        where: { code: 'us_class' },
        select: { defaultPageSize: true, maxPageSize: true, maxPages: true }
      });
    }
    const hotEnd = performance.now();
    
    console.log(`   冷缓存查询: ${(coldEnd - coldStart).toFixed(2)}ms`);
    console.log(`   热缓存平均: ${((hotEnd - hotStart) / 10).toFixed(2)}ms`);

    // 6. 内存使用分析
    console.log('\n🧠 内存使用分析:');
    
    const configSize = JSON.stringify({
      defaultPageSize: 20,
      maxPageSize: 100,
      maxPages: 50
    }).length;
    
    const globalConfigSize = JSON.stringify(GLOBAL_CONFIG).length;
    
    console.log(`   单个配置大小: ${configSize} bytes`);
    console.log(`   全局配置大小: ${globalConfigSize} bytes`);
    console.log(`   内存节省: 无需存储多个配置`);

    // 7. 建议
    console.log('\n💡 性能建议:');
    
    if (avgTime > 1) {
      console.log('   ⚠️  数据库查询开销较大，建议使用全局配置');
    } else {
      console.log('   ✅ 数据库查询开销可接受，但全局配置仍更优');
    }
    
    console.log(`   🎯 推荐方案: 全局统一配置`);
    console.log(`   📈 预期性能提升: ${speedup.toFixed(0)}x`);
    console.log(`   🔧 实施难度: 低（只需修改常量）`);

  } catch (error) {
    console.error('❌ 性能测试失败:', error);
  } finally {
    await db.$disconnect();
  }
}

performanceTest().catch(console.error);

#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

async function debugCountryCodePerformance() {
  console.log('🔍 调试 country_code 筛选器性能问题...');
  
  try {
    // 1. 检查 country_code 数据量
    console.log('\n📊 1. 检查 country_code 数据量...');
    
    const countryStats = await db.uSPremarketNotification.groupBy({
      by: ['country_code'],
      _count: { country_code: true },
      orderBy: { _count: { country_code: 'desc' } }
    });
    
    console.log(`总共有 ${countryStats.length} 个不同的 country_code 值`);
    console.log('前 10 个最多的国家:');
    countryStats.slice(0, 10).forEach((item, index) => {
      console.log(`  ${index + 1}. ${item.country_code || 'N/A'}: ${item._count.country_code} 条`);
    });
    
    // 2. 检查可能的性能问题
    console.log('\n⚠️  2. 性能问题分析...');
    
    if (countryStats.length > 50) {
      console.log(`❌ 选项过多: ${countryStats.length} 个选项可能导致渲染卡顿`);
      console.log('建议: 考虑分页或搜索功能');
    }
    
    // 检查是否有异常长的国家代码
    const longCodes = countryStats.filter(item => 
      item.country_code && item.country_code.length > 10
    );
    
    if (longCodes.length > 0) {
      console.log(`⚠️  发现 ${longCodes.length} 个异常长的国家代码:`);
      longCodes.forEach(item => {
        console.log(`  "${item.country_code}" (${item.country_code?.length} 字符)`);
      });
    }
    
    // 3. 检查空值情况
    console.log('\n🔍 3. 检查空值情况...');
    
    const nullCount = await db.uSPremarketNotification.count({
      where: { 
        OR: [
          { country_code: null },
          { country_code: '' }
        ]
      }
    });
    
    console.log(`空值记录数: ${nullCount}`);
    
    // 4. 模拟前端渲染负载
    console.log('\n🎨 4. 模拟前端渲染负载...');
    
    const startTime = Date.now();
    
    // 模拟 MultiSelect 组件需要处理的数据结构
    const options = countryStats.map(item => ({
      value: item.country_code || 'N/A',
      label: item.country_code || 'N/A',
      count: item._count.country_code
    }));
    
    const endTime = Date.now();
    console.log(`数据转换耗时: ${endTime - startTime}ms`);
    console.log(`需要渲染的选项数: ${options.length}`);
    
    // 5. 检查可能的无限循环
    console.log('\n🔄 5. 检查可能的无限循环问题...');
    
    console.log('可能的问题源:');
    console.log('1. ❓ useEffect 依赖循环');
    console.log('2. ❓ fetchDynamicCounts 重复调用');
    console.log('3. ❓ 大量 DOM 元素渲染卡顿');
    console.log('4. ❓ 事件处理函数重复绑定');
    
    // 6. 检查 meta API 性能
    console.log('\n🌐 6. 检查 meta API 性能...');
    
    try {
      const apiStartTime = Date.now();
      const response = await fetch('http://localhost:3001/api/meta/us_pmn');
      const result = await response.json();
      const apiEndTime = Date.now();
      
      if (result.success) {
        const countryOptions = result.dataWithCounts?.country_code || [];
        console.log(`✅ Meta API 响应时间: ${apiEndTime - apiStartTime}ms`);
        console.log(`返回的 country_code 选项数: ${countryOptions.length}`);
        
        if (apiEndTime - apiStartTime > 2000) {
          console.log('⚠️  API 响应时间过长，可能导致前端卡顿');
        }
      } else {
        console.log('❌ Meta API 失败:', result.error);
      }
    } catch (error) {
      console.log('❌ Meta API 请求失败:', error.message);
    }
    
    // 7. 建议的解决方案
    console.log('\n💡 7. 建议的解决方案...');
    
    if (countryStats.length > 50) {
      console.log('🔧 性能优化建议:');
      console.log('1. 添加虚拟滚动 (react-window)');
      console.log('2. 添加搜索过滤功能');
      console.log('3. 分页显示选项');
      console.log('4. 延迟加载选项');
      console.log('5. 限制显示最常用的前 20-30 个选项');
    }
    
    console.log('\n🔧 无限循环排查:');
    console.log('1. 检查 useEffect 依赖数组');
    console.log('2. 检查 fetchDynamicCounts 调用时机');
    console.log('3. 添加防抖 (debounce) 机制');
    console.log('4. 检查事件处理函数是否重复绑定');
    
    // 8. 对比其他数据库
    console.log('\n📊 8. 对比其他数据库的选项数量...');
    
    try {
      const usClassDeviceClass = await db.uSClassification.groupBy({
        by: ['deviceclass'],
        _count: { deviceclass: true }
      });
      
      console.log(`us_class deviceclass 选项数: ${usClassDeviceClass.length}`);
      
      if (usClassDeviceClass.length < countryStats.length) {
        console.log('✅ us_class 的选项数较少，可能不会卡顿');
      } else {
        console.log('⚠️  us_class 的选项数也很多，可能也有性能问题');
      }
    } catch (error) {
      console.log('⚠️  无法获取 us_class 数据进行对比');
    }
    
  } catch (error) {
    console.error('❌ 调试失败:', error);
  } finally {
    await db.$disconnect();
  }
}

debugCountryCodePerformance().catch(console.error);

import { PrismaClient } from '@prisma/client';
import { readFileSync } from 'fs';
import { join } from 'path';

const db = new PrismaClient();

async function removePaginationFields() {
  console.log('🗑️  开始删除 DatabaseConfig 表中的翻页配置字段...\n');

  try {
    // 1. 显示删除前的表结构
    console.log('📋 删除前的表结构:');
    const beforeColumns = await db.$queryRaw`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'DatabaseConfig' 
        AND table_schema = 'public'
      ORDER BY ordinal_position;
    ` as any[];

    console.log('┌─────────────────────┬─────────────┬─────────────┬─────────────────┐');
    console.log('│ 字段名              │ 数据类型    │ 可为空      │ 默认值          │');
    console.log('├─────────────────────┼─────────────┼─────────────┼─────────────────┤');
    
    beforeColumns.forEach(col => {
      const name = String(col.column_name).padEnd(19);
      const type = String(col.data_type).padEnd(11);
      const nullable = String(col.is_nullable).padEnd(11);
      const defaultVal = String(col.column_default || '').padEnd(15);
      console.log(`│ ${name} │ ${type} │ ${nullable} │ ${defaultVal} │`);
    });
    console.log('└─────────────────────┴─────────────┴─────────────┴─────────────────┘');

    // 2. 执行删除操作
    console.log('\n🔧 执行字段删除操作...');

    console.log('📝 删除 defaultPageSize 字段...');
    try {
      await db.$executeRaw`ALTER TABLE "DatabaseConfig" DROP COLUMN IF EXISTS "defaultPageSize";`;
      console.log('✅ defaultPageSize 字段删除成功');
    } catch (error) {
      console.log('⚠️  defaultPageSize 字段删除失败 (可能不存在):', error.message);
    }

    console.log('📝 删除 maxPageSize 字段...');
    try {
      await db.$executeRaw`ALTER TABLE "DatabaseConfig" DROP COLUMN IF EXISTS "maxPageSize";`;
      console.log('✅ maxPageSize 字段删除成功');
    } catch (error) {
      console.log('⚠️  maxPageSize 字段删除失败 (可能不存在):', error.message);
    }

    console.log('📝 删除 maxPages 字段...');
    try {
      await db.$executeRaw`ALTER TABLE "DatabaseConfig" DROP COLUMN IF EXISTS "maxPages";`;
      console.log('✅ maxPages 字段删除成功');
    } catch (error) {
      console.log('⚠️  maxPages 字段删除失败 (可能不存在):', error.message);
    }

    // 3. 显示删除后的表结构
    console.log('\n📋 删除后的表结构:');
    const afterColumns = await db.$queryRaw`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'DatabaseConfig' 
        AND table_schema = 'public'
      ORDER BY ordinal_position;
    ` as any[];

    console.log('┌─────────────────────┬─────────────┬─────────────┬─────────────────┐');
    console.log('│ 字段名              │ 数据类型    │ 可为空      │ 默认值          │');
    console.log('├─────────────────────┼─────────────┼─────────────┼─────────────────┤');
    
    afterColumns.forEach(col => {
      const name = String(col.column_name).padEnd(19);
      const type = String(col.data_type).padEnd(11);
      const nullable = String(col.is_nullable).padEnd(11);
      const defaultVal = String(col.column_default || '').padEnd(15);
      console.log(`│ ${name} │ ${type} │ ${nullable} │ ${defaultVal} │`);
    });
    console.log('└─────────────────────┴─────────────┴─────────────┴─────────────────┘');

    // 4. 验证数据完整性
    console.log('\n🔍 验证数据完整性...');
    const configs = await db.$queryRaw`
      SELECT 
        "code",
        "name", 
        "category",
        "description",
        "accessLevel",
        "isActive",
        "createdAt",
        "updatedAt"
      FROM "DatabaseConfig" 
      WHERE "isActive" = true
      ORDER BY "code";
    ` as any[];

    console.log('\n📊 现有数据库配置:');
    console.log('┌─────────────┬──────────────────────┬─────────────┬─────────────┐');
    console.log('│ 数据库代码  │ 数据库名称           │ 分类        │ 访问级别    │');
    console.log('├─────────────┼──────────────────────┼─────────────┼─────────────┤');
    
    configs.forEach(config => {
      const code = String(config.code).padEnd(11);
      const name = String(config.name).padEnd(20);
      const category = String(config.category || '').padEnd(11);
      const accessLevel = String(config.accessLevel).padEnd(11);
      
      console.log(`│ ${code} │ ${name} │ ${category} │ ${accessLevel} │`);
    });
    
    console.log('└─────────────┴──────────────────────┴─────────────┴─────────────────┘');

    // 5. 对比字段变化
    console.log('\n📈 字段变化对比:');
    const beforeFieldNames = beforeColumns.map(col => col.column_name);
    const afterFieldNames = afterColumns.map(col => col.column_name);
    
    const removedFields = beforeFieldNames.filter(name => !afterFieldNames.includes(name));
    const remainingFields = afterFieldNames.length;
    
    console.log(`   删除前字段数: ${beforeFieldNames.length}`);
    console.log(`   删除后字段数: ${remainingFields}`);
    console.log(`   删除的字段: ${removedFields.join(', ') || '无'}`);

    if (removedFields.length > 0) {
      console.log('\n✅ 翻页配置字段删除完成！');
      console.log('\n📋 删除的字段:');
      removedFields.forEach(field => {
        console.log(`   • ${field}`);
      });
    } else {
      console.log('\n⚠️  没有找到需要删除的翻页字段');
    }

    console.log('\n🎯 下一步操作:');
    console.log('   1. 更新 prisma/schema.prisma 文件');
    console.log('   2. 运行 npx prisma generate 重新生成客户端');
    console.log('   3. 验证应用程序正常运行');

  } catch (error) {
    console.error('❌ 删除操作失败:', error);
    throw error;
  } finally {
    await db.$disconnect();
  }
}

// 运行脚本
removePaginationFields().catch(console.error);

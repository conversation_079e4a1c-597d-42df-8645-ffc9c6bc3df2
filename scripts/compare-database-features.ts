#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

async function compareDatabaseFeatures() {
  console.log('🔍 详细对比 us_pmn 和 us_class 的功能实现...');
  
  try {
    // 1. 对比 DatabaseConfig 配置
    console.log('\n📋 1. 对比 DatabaseConfig 配置...');
    const dbConfigs = await db.databaseConfig.findMany({
      where: { 
        code: { in: ['us_pmn', 'us_class'] },
        isActive: true 
      }
    });
    
    dbConfigs.forEach(config => {
      console.log(`\n  ${config.code}:`);
      console.log(`    名称: ${config.name}`);
      console.log(`    分类: ${config.category}`);
      console.log(`    访问级别: ${config.accessLevel}`);
      console.log(`    表名: ${config.tableName}`);
      console.log(`    模型名: ${config.modelName}`);
      console.log(`    默认排序: ${JSON.stringify(config.defaultSort)}`);
      console.log(`    导出限制: ${config.defaultExportLimit}/${config.maxExportLimit}`);
    });
    
    // 2. 对比 FieldConfig 详细配置
    console.log('\n🔧 2. 对比 FieldConfig 详细配置...');
    
    for (const dbCode of ['us_pmn', 'us_class']) {
      console.log(`\n  === ${dbCode} 字段配置 ===`);
      
      const fields = await db.fieldConfig.findMany({
        where: { databaseCode: dbCode, isActive: true },
        select: {
          fieldName: true,
          displayName: true,
          fieldType: true,
          isVisible: true,
          isFilterable: true,
          isSearchable: true,
          isSortable: true,
          filterType: true,
          searchType: true,
          listOrder: true,
          sortOrder: true,
          // 统计功能配置
          isStatisticsEnabled: true,
          statisticsOrder: true,
          statisticsDisplayName: true,
          statisticsDefaultLimit: true,
          statisticsMaxLimit: true,
          // 导出功能配置
          isExportable: true,
          exportOrder: true,
          exportDisplayName: true
        },
        orderBy: { listOrder: 'asc' }
      });
      
      console.log(`    总字段数: ${fields.length}`);
      console.log(`    可见字段: ${fields.filter(f => f.isVisible).length}`);
      console.log(`    可筛选字段: ${fields.filter(f => f.isFilterable).length}`);
      console.log(`    可搜索字段: ${fields.filter(f => f.isSearchable).length}`);
      console.log(`    可排序字段: ${fields.filter(f => f.isSortable).length}`);
      console.log(`    启用统计字段: ${fields.filter(f => f.isStatisticsEnabled).length}`);
      console.log(`    可导出字段: ${fields.filter(f => f.isExportable).length}`);
      
      // 详细列出筛选字段
      const filterableFields = fields.filter(f => f.isFilterable);
      console.log(`\n    筛选字段详情:`);
      filterableFields.forEach((field, index) => {
        console.log(`      ${index + 1}. ${field.fieldName} (${field.displayName})`);
        console.log(`         类型: ${field.filterType}, 可见: ${field.isVisible}`);
      });
      
      // 详细列出统计字段
      const statsFields = fields.filter(f => f.isStatisticsEnabled);
      if (statsFields.length > 0) {
        console.log(`\n    统计字段详情:`);
        statsFields.forEach((field, index) => {
          console.log(`      ${index + 1}. ${field.statisticsDisplayName || field.displayName}`);
          console.log(`         字段: ${field.fieldName}, 默认显示: ${field.statisticsDefaultLimit}, 最大: ${field.statisticsMaxLimit}`);
        });
      } else {
        console.log(`\n    ⚠️  未配置统计字段`);
      }
    }
    
    // 3. 测试 API 功能
    console.log('\n🌐 3. 测试 API 功能对比...');
    
    for (const dbCode of ['us_pmn', 'us_class']) {
      console.log(`\n  === ${dbCode} API 测试 ===`);
      
      try {
        // 测试 meta API
        const metaResponse = await fetch(`http://localhost:3001/api/meta/${dbCode}`);
        const metaResult = await metaResponse.json();
        
        if (metaResult.success) {
          const filterFields = Object.keys(metaResult.data || {});
          const withCountsFields = Object.keys(metaResult.dataWithCounts || {});
          console.log(`    Meta API: ✅ 成功`);
          console.log(`      筛选字段数: ${filterFields.length}`);
          console.log(`      带计数字段数: ${withCountsFields.length}`);
          console.log(`      筛选字段: ${filterFields.slice(0, 3).join(', ')}${filterFields.length > 3 ? '...' : ''}`);
        } else {
          console.log(`    Meta API: ❌ 失败 - ${metaResult.error}`);
        }
        
        // 测试 stats API
        const statsResponse = await fetch(`http://localhost:3001/api/stats/${dbCode}`);
        const statsResult = await statsResponse.json();
        
        if (statsResult.success) {
          console.log(`    Stats API: ✅ 成功`);
          console.log(`      总记录数: ${statsResult.data.totalCount}`);
          console.log(`      活跃记录数: ${statsResult.data.activeCount || 'N/A'}`);
        } else {
          console.log(`    Stats API: ❌ 失败 - ${statsResult.error}`);
        }
        
        // 测试可配置统计 API
        const configStatsResponse = await fetch(`http://localhost:3001/api/stats/${dbCode}/configurable`);
        const configStatsResult = await configStatsResponse.json();
        
        if (configStatsResult.success) {
          const statsCount = configStatsResult.data?.length || 0;
          console.log(`    可配置统计 API: ✅ 成功`);
          console.log(`      统计项数量: ${statsCount}`);
        } else {
          console.log(`    可配置统计 API: ❌ 失败 - ${configStatsResult.error}`);
        }
        
        // 测试数据 API
        const dataResponse = await fetch(`http://localhost:3001/api/data/${dbCode}?page=1&limit=5`);
        const dataResult = await dataResponse.json();
        
        if (dataResult.success) {
          console.log(`    Data API: ✅ 成功`);
          console.log(`      返回记录数: ${dataResult.data?.length || 0}`);
          console.log(`      总页数: ${dataResult.pagination?.totalPages || 0}`);
        } else {
          console.log(`    Data API: ❌ 失败 - ${dataResult.error}`);
        }
        
      } catch (error) {
        console.log(`    API 测试失败: ${error.message}`);
      }
    }
    
    // 4. 功能差异分析
    console.log('\n📊 4. 功能差异分析...');
    
    const usPmnFields = await db.fieldConfig.findMany({
      where: { databaseCode: 'us_pmn', isActive: true }
    });
    
    const usClassFields = await db.fieldConfig.findMany({
      where: { databaseCode: 'us_class', isActive: true }
    });
    
    console.log('\n  功能对比表:');
    console.log('  ┌─────────────────────┬─────────┬──────────┐');
    console.log('  │ 功能                │ us_pmn  │ us_class │');
    console.log('  ├─────────────────────┼─────────┼──────────┤');
    console.log(`  │ 总字段数            │ ${usPmnFields.length.toString().padEnd(7)} │ ${usClassFields.length.toString().padEnd(8)} │`);
    console.log(`  │ 可见字段            │ ${usPmnFields.filter(f => f.isVisible).length.toString().padEnd(7)} │ ${usClassFields.filter(f => f.isVisible).length.toString().padEnd(8)} │`);
    console.log(`  │ 可筛选字段          │ ${usPmnFields.filter(f => f.isFilterable).length.toString().padEnd(7)} │ ${usClassFields.filter(f => f.isFilterable).length.toString().padEnd(8)} │`);
    console.log(`  │ 可搜索字段          │ ${usPmnFields.filter(f => f.isSearchable).length.toString().padEnd(7)} │ ${usClassFields.filter(f => f.isSearchable).length.toString().padEnd(8)} │`);
    console.log(`  │ 可排序字段          │ ${usPmnFields.filter(f => f.isSortable).length.toString().padEnd(7)} │ ${usClassFields.filter(f => f.isSortable).length.toString().padEnd(8)} │`);
    console.log(`  │ 启用统计字段        │ ${usPmnFields.filter(f => f.isStatisticsEnabled).length.toString().padEnd(7)} │ ${usClassFields.filter(f => f.isStatisticsEnabled).length.toString().padEnd(8)} │`);
    console.log(`  │ 可导出字段          │ ${usPmnFields.filter(f => f.isExportable).length.toString().padEnd(7)} │ ${usClassFields.filter(f => f.isExportable).length.toString().padEnd(8)} │`);
    console.log('  └─────────────────────┴─────────┴──────────┘');
    
    // 5. 建议改进
    console.log('\n💡 5. 建议改进...');
    
    const usPmnStatsCount = usPmnFields.filter(f => f.isStatisticsEnabled).length;
    const usClassStatsCount = usClassFields.filter(f => f.isStatisticsEnabled).length;
    
    if (usPmnStatsCount < usClassStatsCount) {
      console.log('  🔧 us_pmn 缺少统计功能配置，建议复用 us_class 的统计配置');
    }
    
    const usPmnMultiSelectCount = usPmnFields.filter(f => f.filterType === 'multi_select').length;
    const usClassMultiSelectCount = usClassFields.filter(f => f.filterType === 'multi_select').length;
    
    console.log(`\n  Multi-select 筛选器对比:`);
    console.log(`    us_pmn: ${usPmnMultiSelectCount} 个`);
    console.log(`    us_class: ${usClassMultiSelectCount} 个`);
    
  } catch (error) {
    console.error('❌ 对比过程中发生错误:', error);
  } finally {
    await db.$disconnect();
  }
}

compareDatabaseFeatures().catch(console.error);

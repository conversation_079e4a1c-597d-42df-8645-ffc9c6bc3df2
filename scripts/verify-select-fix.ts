#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

async function verifySelectFix() {
  console.log('🔍 验证 select 类型筛选器动态计数修复...');
  
  try {
    // 1. 验证测试数据
    console.log('\n📊 1. 验证测试数据...');
    
    // 德国数据总数
    const deTotal = await db.uSPremarketNotification.count({
      where: { country_code: 'DE' }
    });
    console.log(`DE 总记录数: ${deTotal}`);
    
    // 德国数据中的 expeditedreview 分布
    const deExpedited = await db.uSPremarketNotification.groupBy({
      by: ['expeditedreview'],
      where: { country_code: 'DE' },
      _count: { expeditedreview: true },
      orderBy: { _count: { expeditedreview: 'desc' } }
    });
    
    console.log('DE 数据中的 expeditedreview 分布:');
    deExpedited.forEach(item => {
      const value = item.expeditedreview || 'N/A';
      console.log(`  ${value}: ${item._count.expeditedreview} 条`);
    });
    
    // 2. 验证 API 响应
    console.log('\n🌐 2. 验证 dynamic-counts API...');
    
    try {
      const filters = { country_code: ['DE'] };
      const params = new URLSearchParams({
        field: 'expeditedreview',
        filters: JSON.stringify(filters)
      });
      
      const response = await fetch(`http://localhost:3001/api/meta/us_pmn/dynamic-counts?${params}`);
      const result = await response.json();
      
      if (result.success) {
        console.log('✅ API 返回结果:');
        result.data.forEach((item: any) => {
          console.log(`  ${item.value}: ${item.count} 条`);
        });
        
        const apiTotal = result.data.reduce((sum: number, item: any) => sum + item.count, 0);
        console.log(`API 总计: ${apiTotal}, 实际总计: ${deTotal}`);
        
        if (apiTotal === deTotal) {
          console.log('✅ API 计数正确');
        } else {
          console.log('❌ API 计数不匹配');
        }
      } else {
        console.log('❌ API 失败:', result.error);
      }
    } catch (error) {
      console.log('❌ API 请求失败:', error.message);
    }
    
    // 3. 验证修复内容
    console.log('\n🔧 3. 修复内容总结...');
    console.log('✅ 修复前问题:');
    console.log('  - select 类型使用 optionsWithCounts (静态计数)');
    console.log('  - multi_select 类型使用 mergedOptions (动态计数)');
    console.log('  - 导致 select 类型筛选器不会联动更新');
    
    console.log('\n✅ 修复后改进:');
    console.log('  - select 和 multi_select 都使用 mergedOptions');
    console.log('  - mergedOptions 优先使用 dynamicCounts，回退到 optionsWithCounts');
    console.log('  - 所有筛选器类型都支持联动更新');
    
    // 4. 验证其他 select 字段
    console.log('\n📋 4. 验证其他 select 字段...');
    
    const selectFields = await db.fieldConfig.findMany({
      where: {
        databaseCode: 'us_pmn',
        filterType: 'select',
        isFilterable: true,
        isActive: true
      },
      select: {
        fieldName: true,
        displayName: true
      }
    });
    
    console.log(`us_pmn 有 ${selectFields.length} 个 select 类型筛选字段:`);
    selectFields.forEach((field, index) => {
      console.log(`  ${index + 1}. ${field.fieldName} (${field.displayName})`);
    });
    
    console.log('\n💡 现在这些字段都支持联动更新！');
    
    // 5. 测试建议
    console.log('\n🧪 5. 测试建议...');
    console.log('请按以下步骤测试:');
    console.log('1. 访问: http://localhost:3001/data/list/us_pmn');
    console.log('2. 选择 Country Code = DE (应该显示 2353 条)');
    console.log('3. 观察 Expedited Review 选项:');
    console.log('   - 应该显示: N/A (2353)');
    console.log('   - 而不是: Y(28) N/A(172778)');
    console.log('4. 测试其他 select 字段的联动效果');
    
    // 6. 对比验证
    console.log('\n📊 6. 功能对比...');
    
    const multiSelectFields = await db.fieldConfig.count({
      where: {
        databaseCode: 'us_pmn',
        filterType: 'multi_select',
        isFilterable: true,
        isActive: true
      }
    });
    
    const checkboxFields = await db.fieldConfig.count({
      where: {
        databaseCode: 'us_pmn',
        filterType: 'checkbox',
        isFilterable: true,
        isActive: true
      }
    });
    
    console.log('us_pmn 支持联动的筛选器类型:');
    console.log(`  select: ${selectFields.length} 个 ✅`);
    console.log(`  multi_select: ${multiSelectFields} 个 ✅`);
    console.log(`  checkbox: ${checkboxFields} 个 ✅`);
    console.log(`  总计: ${selectFields.length + multiSelectFields + checkboxFields} 个支持联动`);
    
  } catch (error) {
    console.error('❌ 验证失败:', error);
  } finally {
    await db.$disconnect();
  }
}

verifySelectFix().catch(console.error);

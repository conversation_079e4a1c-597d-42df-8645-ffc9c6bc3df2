#!/usr/bin/env tsx

import { ConfigCacheService } from '../src/lib/configCache';
import { ConfigManager } from './config-manager';
import { db } from '../src/lib/prisma';

/**
 * 配置刷新测试工具
 * 测试配置更新后的生效机制
 */

async function testConfigRefresh() {
  console.log('🧪 测试配置刷新机制...\n');

  try {
    const testDatabase = 'us_class';
    const testField = 'deviceclass';

    // 步骤1: 查看当前配置
    console.log('📋 步骤1: 查看当前配置');
    const currentConfig = await getCurrentConfig(testDatabase, testField);
    console.log(`   当前 ${testField} 的筛选类型: ${currentConfig?.filterType}`);

    // 步骤2: 测试缓存状态
    console.log('\n🔍 步骤2: 测试缓存状态');
    await testCacheStatus(testDatabase);

    // 步骤3: 模拟配置更新
    console.log('\n🔧 步骤3: 模拟配置更新');
    const originalFilterType = currentConfig?.filterType;
    const newFilterType = originalFilterType === 'multi_select' ? 'select' : 'multi_select';
    
    await updateFieldConfig(testDatabase, testField, newFilterType);
    console.log(`   ✅ 已将 ${testField} 的筛选类型更新为: ${newFilterType}`);

    // 步骤4: 测试缓存中的配置（应该还是旧的）
    console.log('\n🔍 步骤4: 测试缓存中的配置');
    const cachedConfig = await getCachedConfig(testDatabase, testField);
    console.log(`   缓存中的筛选类型: ${cachedConfig?.filterType}`);
    
    if (cachedConfig?.filterType === originalFilterType) {
      console.log('   ✅ 缓存机制正常：配置更新后缓存未立即更新');
    } else {
      console.log('   ⚠️  缓存可能已失效或不存在');
    }

    // 步骤5: 清除缓存
    console.log('\n🧹 步骤5: 清除缓存');
    await ConfigCacheService.clearDatabaseCache(testDatabase);
    console.log('   ✅ 已清除缓存');

    // 步骤6: 测试清除缓存后的配置（应该是新的）
    console.log('\n🔍 步骤6: 测试清除缓存后的配置');
    const refreshedConfig = await getCachedConfig(testDatabase, testField);
    console.log(`   刷新后的筛选类型: ${refreshedConfig?.filterType}`);
    
    if (refreshedConfig?.filterType === newFilterType) {
      console.log('   ✅ 缓存清除生效：配置已更新');
    } else {
      console.log('   ❌ 缓存清除可能失败');
    }

    // 步骤7: 测试 API 刷新
    console.log('\n🌐 步骤7: 测试 API 刷新');
    await testAPIRefresh();

    // 步骤8: 恢复原始配置
    console.log('\n🔄 步骤8: 恢复原始配置');
    await updateFieldConfig(testDatabase, testField, originalFilterType!);
    await ConfigCacheService.clearDatabaseCache(testDatabase);
    console.log(`   ✅ 已恢复 ${testField} 的筛选类型为: ${originalFilterType}`);

    console.log('\n🎉 配置刷新测试完成!');
    
    // 总结
    console.log('\n📚 配置生效方式总结:');
    console.log('   1. 🕐 自动生效: 等待 5-10 分钟缓存过期');
    console.log('   2. ⚡ 手动清缓存: 立即生效');
    console.log('   3. 🔄 重启应用: 完全重置');
    console.log('   4. 🌐 API 刷新: 调用刷新接口');

  } catch (error) {
    console.error('\n❌ 测试失败:', error);
    throw error;
  } finally {
    await db.$disconnect();
  }
}

/**
 * 获取当前配置
 */
async function getCurrentConfig(databaseCode: string, fieldName: string) {
  const config = await db.fieldConfig.findFirst({
    where: {
      databaseCode,
      fieldName,
      isActive: true,
    },
    select: {
      fieldName: true,
      displayName: true,
      filterType: true,
      updatedAt: true,
    },
  });
  return config;
}

/**
 * 获取缓存中的配置
 */
async function getCachedConfig(databaseCode: string, fieldName: string) {
  try {
    const config = await ConfigCacheService.getDatabaseConfig(databaseCode);
    const field = config.fields.find(f => f.fieldName === fieldName);
    return field;
  } catch (error) {
    console.error('   获取缓存配置失败:', error);
    return null;
  }
}

/**
 * 更新字段配置
 */
async function updateFieldConfig(databaseCode: string, fieldName: string, filterType: string) {
  await db.fieldConfig.updateMany({
    where: {
      databaseCode,
      fieldName,
      isActive: true,
    },
    data: {
      filterType,
      updatedAt: new Date(),
    },
  });
}

/**
 * 测试缓存状态
 */
async function testCacheStatus(databaseCode: string) {
  try {
    const config = await ConfigCacheService.getDatabaseConfig(databaseCode);
    console.log(`   ✅ 缓存中有 ${config.fields.length} 个字段配置`);
    
    // 显示缓存的字段信息
    const filterableFields = config.fields.filter(f => f.isFilterable);
    console.log(`   📊 可筛选字段: ${filterableFields.map(f => `${f.fieldName}(${f.filterType})`).join(', ')}`);
    
  } catch (error) {
    console.log('   ⚠️  缓存可能不存在或已失效');
  }
}

/**
 * 测试 API 刷新
 */
async function testAPIRefresh() {
  try {
    const response = await fetch('http://localhost:3000/api/admin/refresh-config', {
      method: 'POST',
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('   ✅ API 刷新成功');
      console.log(`   📊 刷新统计: ${data.stats?.configCount} 个配置`);
    } else {
      console.log(`   ❌ API 刷新失败: ${response.status}`);
    }
  } catch (error) {
    console.log('   ❌ API 刷新请求失败:', error.message);
  }
}

/**
 * 显示缓存管理命令
 */
function showCacheCommands() {
  console.log(`
🛠️  配置缓存管理命令

📋 查看配置:
   npx tsx -e "
   import { ConfigManager } from './scripts/config-manager';
   await ConfigManager.validateConfig('us_class');
   "

🧹 清除缓存:
   npx tsx scripts/clear-redis-cache.ts

🔄 刷新配置:
   curl -X POST http://localhost:3000/api/admin/refresh-config

📊 查看缓存状态:
   npx tsx scripts/cache-management.ts status

⚡ 立即生效配置更新:
   1. 修改配置
   2. npx tsx scripts/clear-redis-cache.ts
   3. 配置立即生效

🕐 自动生效时间:
   - Redis 缓存: 5 分钟
   - 内存缓存: 10 分钟
`);
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help')) {
    showCacheCommands();
  } else {
    await testConfigRefresh();
  }
}

// 执行测试
if (require.main === module) {
  main()
    .then(() => {
      console.log('\n✨ 测试完成!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 测试失败:', error);
      process.exit(1);
    });
}

export { testConfigRefresh };

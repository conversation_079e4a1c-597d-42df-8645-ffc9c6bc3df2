#!/usr/bin/env tsx

import { db } from './src/lib/prisma';

async function testUSClassFilterIssue() {
  console.log('🔍 测试 us_class 器械类别筛选问题...');
  
  try {
    // 1. 检查 us_class 数据库的基本信息
    console.log('\n📊 1. 检查 us_class 数据库基本信息...');
    
    const totalRecords = await db.uSClass.count();
    console.log(`   总记录数: ${totalRecords}`);
    
    // 检查器械类别分布
    const deviceClassStats = await db.uSClass.groupBy({
      by: ['deviceclass'],
      _count: { deviceclass: true },
      where: { deviceclass: { not: null } },
      orderBy: { _count: { deviceclass: 'desc' } }
    });
    
    console.log('\n   器械类别分布:');
    deviceClassStats.forEach(stat => {
      const value = stat.deviceclass || 'N/A';
      console.log(`   ${value}: ${stat._count.deviceclass} 条`);
    });
    
    // 找出类别3的记录数（用户提到的522条）
    const class3Count = deviceClassStats.find(stat => stat.deviceclass === '3')?._count.deviceclass || 0;
    console.log(`\n   📌 器械类别为 "3" 的记录数: ${class3Count}`);
    
    // 2. 检查医学专科在器械类别3下的分布
    console.log('\n📊 2. 检查器械类别为 "3" 时的医学专科分布...');
    
    const class3MedicalSpecialty = await db.uSClass.groupBy({
      by: ['medicalspecialty'],
      where: { 
        deviceclass: '3',
        medicalspecialty: { not: null, not: '' }
      },
      _count: { medicalspecialty: true },
      orderBy: { _count: { medicalspecialty: 'desc' } },
      take: 10
    });
    
    console.log('   器械类别=3 时的医学专科分布（前10）:');
    let class3MedicalSpecialtyTotal = 0;
    class3MedicalSpecialty.forEach(stat => {
      const count = stat._count.medicalspecialty;
      class3MedicalSpecialtyTotal += count;
      console.log(`   ${stat.medicalspecialty}: ${count} 条`);
    });
    console.log(`   医学专科总计: ${class3MedicalSpecialtyTotal}`);
    
    // 3. 检查审查小组在器械类别3下的分布
    console.log('\n📊 3. 检查器械类别为 "3" 时的审查小组分布...');
    
    const class3ReviewPanel = await db.uSClass.groupBy({
      by: ['review_panel'],
      where: { 
        deviceclass: '3',
        review_panel: { not: null, not: '' }
      },
      _count: { review_panel: true },
      orderBy: { _count: { review_panel: 'desc' } },
      take: 10
    });
    
    console.log('   器械类别=3 时的审查小组分布（前10）:');
    let class3ReviewPanelTotal = 0;
    class3ReviewPanel.forEach(stat => {
      const count = stat._count.review_panel;
      class3ReviewPanelTotal += count;
      console.log(`   ${stat.review_panel}: ${count} 条`);
    });
    console.log(`   审查小组总计: ${class3ReviewPanelTotal}`);
    
    // 4. 检查第三方标志在器械类别3下的分布
    console.log('\n📊 4. 检查器械类别为 "3" 时的第三方标志分布...');
    
    const class3ThirdParty = await db.uSClass.groupBy({
      by: ['thirdpartyflag'],
      where: { deviceclass: '3' },
      _count: { thirdpartyflag: true },
      orderBy: { _count: { thirdpartyflag: 'desc' } }
    });
    
    console.log('   器械类别=3 时的第三方标志分布:');
    let class3ThirdPartyTotal = 0;
    class3ThirdParty.forEach(stat => {
      const value = stat.thirdpartyflag || 'N/A';
      const count = stat._count.thirdpartyflag;
      class3ThirdPartyTotal += count;
      console.log(`   ${value}: ${count} 条`);
    });
    console.log(`   第三方标志总计: ${class3ThirdPartyTotal}`);
    
    // 5. 检查GMP豁免在器械类别3下的分布
    console.log('\n📊 5. 检查器械类别为 "3" 时的GMP豁免分布...');
    
    const class3GMPExempt = await db.uSClass.groupBy({
      by: ['gmpexemptflag'],
      where: { deviceclass: '3' },
      _count: { gmpexemptflag: true },
      orderBy: { _count: { gmpexemptflag: 'desc' } }
    });
    
    console.log('   器械类别=3 时的GMP豁免分布:');
    let class3GMPExemptTotal = 0;
    class3GMPExempt.forEach(stat => {
      const value = stat.gmpexemptflag || 'N/A';
      const count = stat._count.gmpexemptflag;
      class3GMPExemptTotal += count;
      console.log(`   ${value}: ${count} 条`);
    });
    console.log(`   GMP豁免总计: ${class3GMPExemptTotal}`);
    
    // 6. 验证用户问题：数据总计应该等于器械类别3的总数
    console.log('\n🎯 6. 验证数据一致性...');
    
    console.log(`   器械类别=3 的总记录数: ${class3Count}`);
    console.log(`   医学专科在类别3下的总计: ${class3MedicalSpecialtyTotal}`);
    console.log(`   审查小组在类别3下的总计: ${class3ReviewPanelTotal}`);
    console.log(`   第三方标志在类别3下的总计: ${class3ThirdPartyTotal}`);
    console.log(`   GMP豁免在类别3下的总计: ${class3GMPExemptTotal}`);
    
    if (class3ThirdPartyTotal === class3Count) {
      console.log('   ✅ 第三方标志统计正确');
    } else {
      console.log('   ❌ 第三方标志统计有问题');
    }
    
    if (class3GMPExemptTotal === class3Count) {
      console.log('   ✅ GMP豁免统计正确');
    } else {
      console.log('   ❌ GMP豁免统计有问题');
    }
    
    // 7. 测试 dynamic-counts API
    console.log('\n🌐 7. 测试 dynamic-counts API...');
    
    // API测试需要服务器运行，跳过此测试
    console.log('   ⚠️ API测试跳过（需要服务器运行）');
    
    // 8. 检查前端配置
    console.log('\n🔧 8. 检查前端配置...');
    
    const filterConfigs = await db.fieldConfig.findMany({
      where: {
        databaseCode: 'us_class',
        isFilterable: true,
        isActive: true
      },
      select: {
        fieldName: true,
        displayName: true,
        filterType: true,
        isFilterable: true
      },
      orderBy: { sortOrder: 'asc' }
    });
    
    console.log('   us_class 筛选器配置:');
    filterConfigs.forEach(config => {
      console.log(`   ${config.fieldName} (${config.displayName}): ${config.filterType}`);
    });
    
    // 9. 分析问题
    console.log('\n🎯 9. 问题分析...');
    
    console.log('用户反馈的问题:');
    console.log('- 选择器械类别3后，医学专科筛选项的数据条数加起来是522条左右');
    console.log('- 但审查小组、GMP豁免、第三方标志的数据统计都大于这个数据');
    console.log('- 说明这些筛选器没有基于器械类别3的选择进行动态更新');
    
    console.log('\n可能的原因:');
    console.log('1. 前端筛选器联动更新机制有问题');
    console.log('2. dynamic-counts API返回的是全库数据而非基于当前筛选条件的数据');
    console.log('3. 筛选器渲染时使用的是静态计数而非动态计数');
    
    if (class3ThirdPartyTotal > 0 && class3GMPExemptTotal > 0) {
      console.log('\n✅ 数据库查询结果显示，基于器械类别3的筛选是正确的');
      console.log('问题很可能出现在前端筛选器联动更新机制上');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await db.$disconnect();
  }
}

testUSClassFilterIssue().catch(console.error); 